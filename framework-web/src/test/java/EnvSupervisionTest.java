import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import org.changneng.framework.frameworkbusiness.service.LocalExamineService;
import org.changneng.framework.frameworkbusiness.entity.LocalCheck;
import org.changneng.framework.frameworkbusiness.entity.LocalCheckItem;
import org.changneng.framework.frameworkcore.utils.ResponseJson;

import java.util.List;

/**
 * 环境监管一件事功能测试
 * 
 * <AUTHOR> Generated
 * @date 2025-01-31
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring-context.xml"})
@WebAppConfiguration
public class EnvSupervisionTest {

    @Autowired
    private LocalExamineService localExamineService;

    /**
     * 测试环境监管一件事数据保存功能
     */
    @Test
    public void testSaveEnvSupervisionItems() {
        try {
            // 准备测试数据
            String localCheckId = "test-local-check-id";
            String envSupervisionData = "[" +
                "{\"configItemId\":\"0_0\",\"itemName\":\"测试检查项1\",\"result\":\"1\",\"problemDesc\":\"测试问题描述1\"}," +
                "{\"configItemId\":\"0_1\",\"itemName\":\"测试检查项2\",\"result\":\"0\",\"problemDesc\":\"测试问题描述2\"}," +
                "{\"configItemId\":\"1_0\",\"itemName\":\"测试检查项3\",\"result\":\"2\",\"problemDesc\":\"\"}" +
                "]";

            // 执行保存操作
            localExamineService.saveEnvSupervisionItems(localCheckId, envSupervisionData);
            
            System.out.println("✅ 环境监管一件事数据保存测试通过");
            
        } catch (Exception e) {
            System.err.println("❌ 环境监管一件事数据保存测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试环境监管一件事数据加载功能
     */
    @Test
    public void testLoadEnvSupervisionItems() {
        try {
            // 准备测试数据
            String localCheckId = "test-local-check-id";
            
            // 执行加载操作
            List<LocalCheckItem> items = localExamineService.loadEnvSupervisionItems(localCheckId);
            
            System.out.println("✅ 环境监管一件事数据加载测试通过，加载到 " + (items != null ? items.size() : 0) + " 条数据");
            
            // 打印加载的数据
            if (items != null && !items.isEmpty()) {
                for (LocalCheckItem item : items) {
                    System.out.println("  - 配置项ID: " + item.getConfigItemId() + 
                                     ", 检查项名称: " + item.getCheckItemName() + 
                                     ", 结果: " + item.getCheckItemResult() + 
                                     ", 问题描述: " + item.getProblemDesc());
                }
            }
            
        } catch (Exception e) {
            System.err.println("❌ 环境监管一件事数据加载测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试完整的保存和加载流程
     */
    @Test
    public void testCompleteEnvSupervisionFlow() {
        try {
            String localCheckId = "test-complete-flow-" + System.currentTimeMillis();
            
            // 1. 保存测试数据
            String envSupervisionData = "[" +
                "{\"configItemId\":\"0_0\",\"itemName\":\"完整流程测试项1\",\"result\":\"1\",\"problemDesc\":\"存在问题\"}," +
                "{\"configItemId\":\"0_1\",\"itemName\":\"完整流程测试项2\",\"result\":\"0\",\"problemDesc\":\"无问题\"}" +
                "]";
            
            localExamineService.saveEnvSupervisionItems(localCheckId, envSupervisionData);
            System.out.println("✅ 步骤1：数据保存成功");
            
            // 2. 加载数据验证
            List<LocalCheckItem> loadedItems = localExamineService.loadEnvSupervisionItems(localCheckId);
            
            if (loadedItems != null && loadedItems.size() == 2) {
                System.out.println("✅ 步骤2：数据加载成功，数据条数正确");
                
                // 验证数据内容
                boolean dataValid = true;
                for (LocalCheckItem item : loadedItems) {
                    if (item.getConfigItemId() == null || item.getCheckItemName() == null || item.getCheckItemResult() == null) {
                        dataValid = false;
                        break;
                    }
                }
                
                if (dataValid) {
                    System.out.println("✅ 步骤3：数据内容验证通过");
                    System.out.println("🎉 完整流程测试全部通过！");
                } else {
                    System.err.println("❌ 步骤3：数据内容验证失败");
                }
                
            } else {
                System.err.println("❌ 步骤2：数据加载失败或数据条数不正确，期望2条，实际" + 
                                 (loadedItems != null ? loadedItems.size() : 0) + "条");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 完整流程测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试空数据处理
     */
    @Test
    public void testEmptyDataHandling() {
        try {
            String localCheckId = "test-empty-data";
            
            // 测试空字符串
            localExamineService.saveEnvSupervisionItems(localCheckId, "");
            System.out.println("✅ 空字符串处理测试通过");
            
            // 测试null
            localExamineService.saveEnvSupervisionItems(localCheckId, null);
            System.out.println("✅ null数据处理测试通过");
            
            // 测试空数组
            localExamineService.saveEnvSupervisionItems(localCheckId, "[]");
            System.out.println("✅ 空数组处理测试通过");
            
            // 测试加载不存在的数据
            List<LocalCheckItem> items = localExamineService.loadEnvSupervisionItems("non-existent-id");
            if (items != null && items.isEmpty()) {
                System.out.println("✅ 不存在数据加载测试通过");
            }
            
            System.out.println("🎉 空数据处理测试全部通过！");
            
        } catch (Exception e) {
            System.err.println("❌ 空数据处理测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
