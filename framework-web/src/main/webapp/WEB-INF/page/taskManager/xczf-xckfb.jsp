<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@page
	import="org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<script type="text/javascript" src="http://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.js"></script>
<link rel="stylesheet" href="http://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.css" />
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
</head>
<%String fastdfs_addr = PropertiesHandlerUtil.getValue(	"fastdfs.nginx.ip", "fastdfs");%>
<c:set var="FASTDFS_ADDR"><%=fastdfs_addr%></c:set>
<%String server_addr = PropertiesHandlerUtil.getValue("server.addr","fastdfs");%>
<c:set var="SERVER_ADDR"><%=server_addr%></c:set>
<style>
.tangram-suggestion-main {
    z-index: 1060;
}

/* 环境监管一件事表单样式 */
#envSupervisionForm .panel-group {
    margin-bottom: 0;
}

#envSupervisionForm .panel {
    border: none;
    border-radius: 0;
    box-shadow: none;
    margin-bottom: 18px;
}

#envSupervisionForm .panel-heading {
    background-color: #ffffff;
    border: 1px solid #e9ecef;
    border-left: 4px solid #17a2b8;
    border-radius: 0;
    padding: 0;
    position: relative;
}

#envSupervisionForm .panel-title {
    margin: 0;
    font-size: 14px;
}

#envSupervisionForm .panel-title a {
    display: block;
    padding: 12px 15px;
    color: #333;
    text-decoration: none;
    /*font-weight: 500;*/
	font-size: 16px;
    position: relative;
}

#envSupervisionForm .panel-title a:hover,
#envSupervisionForm .panel-title a:focus {
    text-decoration: none;
    color: #333;
    background-color: #f1f3f4;
}

#envSupervisionForm .panel-title .collapse-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #17a2b8;
    font-size: 16px;
    transition: transform 0.3s ease;
}

#envSupervisionForm .panel-title a[aria-expanded="true"] .collapse-icon {
    transform: translateY(-50%) rotate(180deg);
}

#envSupervisionForm .panel-title .status-badge {
    position: absolute;
    right: 45px;
    top: 50%;
    transform: translateY(-50%);
    background-color: #6c757d;
    color: white;
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: normal;
}

#envSupervisionForm .panel-collapse {
    border-top: none;
}

#envSupervisionForm .panel-body {
    padding: 0;
    background-color: #fff;
}

/* 不涉及单选框样式 */
#envSupervisionForm .not-involved-checkbox {
    position: absolute;
    right: 50%;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
}

#envSupervisionForm .checkbox-label {
    display: flex;
    align-items: center;
    margin: 0;
    cursor: pointer;
    font-size: 12px;
    color: #666;
    user-select: none;
}

#envSupervisionForm .checkbox-label input[type="checkbox"] {
    margin-right: 5px;
    cursor: pointer;
    transform: scale(1.1);
}

#envSupervisionForm .checkbox-text {
    white-space: nowrap;
}

/* 当显示不涉及单选框时，调整标题文字的右边距，为单选框留出空间 */
#envSupervisionForm .panel-title a.with-checkbox {
    padding-right: 120px;
}


</style>
<script type="text/javascript">
	var SERVER_ADDR = '${SERVER_ADDR}';
	var FASTDFS_ADDR = '${FASTDFS_ADDR}';
</script>
<style>
#leftsead{width:131px;height:143px;position:fixed;bottom:90px;right:10px;}
*html #leftsead{margin-bottom:90px;position:absolute;top:expression(eval(document.documentElement.scrollTop));}
#leftsead li{width:131px;height:60px;}
#leftsead li img{float:right;}
#leftsead li a{height:49px;float:right;display:block;min-width:45px;max-width:131px;}
#leftsead li a .shows{display:block;}
#leftsead li a .hides{margin-right:-143px;cursor:pointer;cursor:hand;}
#leftsead li a.youhui .hides{display:none;position:absolute;right:190px;top:2px;}
.pdfobject-container {
	width: 100%;
	height: 580px;
	margin: 2em 0;
}
</style>
<script src="${webpath }/static/TaskManager/BusinessFlow.js"></script>
<body>
	<div class="main-container">
		<div class="padding-md">
			<jsp:include page="BusinessFlow.jsp"></jsp:include>
		<%-- 	<jsp:include page="../model/checkLawObjectModell.jsp"></jsp:include> --%>
		<%-- 	<jsp:include page="../model/uploadFileModel.jsp"></jsp:include> --%>
			<jsp:include page="../model/recordUserModel.jsp"></jsp:include>
			<!--第三层任务办理row-->
			<div class="row">
				<!--任务办理表单-->
				<div class="col-lg-12">
						<div class="smart-widget widget-light-grey">
							<div class="smart-widget-header font-16">
							<i class="fa fa-comment"></i> 现场执法<c:if test="${localCheak.id ==null  or localCheak.id =='' }"><span id="templetType"></span></c:if>
                               <span class="smart-widget-option" style="margin-top:-3px;">
                               	<c:if test="${localCheak.id ==null  or localCheak.id =='' }">
                               	 <c:if test="${lawObj.taskFromType =='1' || lawObj.taskFromType =='5'}">
                             	  <!-- <button type="button" id ="sysTemplateBtn" class="btn btn-info btn-sm">系统推荐模板</button> -->
                             		<a href ="#"><button type="button" class="btn btn-info btn-sm"
									tabindex="-1" data-toggle="modal"
									data-remote="${webpath}/refineTemplate/sys-refineTemplate-model?status=2"
									data-target="#xttjmb">系统推荐模板</button></a>
                             	 <a href ="#">
                             	 <button type="button" class="btn btn-info btn-sm" data-remote="${webpath}/localExamine/localExamine-custom-page?lawObjectType=${lawObj.lawObjectType}" tabindex="-1" data-toggle="modal" data-target="#zdymb">自定义模板</button>
                             	 </a>
                                 </c:if></c:if>
                                   <span class="refresh-icon-animated"><i class="fa fa-circle-o-notch fa-spin"></i></span>
							</span>
							</div>
						<div class="smart-widget-inner table-responsive">
							<div class="smart-widget-body form-horizontal tab-bigfont">
								<form action="#" method="post" id="localExamineForm">
									<input id="docUrl" type="hidden" name="docUrl"
										value="${localCheak.docUrl}"></input>
										<input  name="token" value="${tokenReport }" type="hidden">
										<input  id ="multiple" name="multiple"  type="hidden" value="${localCheak.multiple}">
										<input id="taskId" type="hidden" name="taskId" value="${lawObj.taskId}"></input>
									    <input id="localCheakId" type="hidden" name="id" value="${localCheak.id}"></input>
									   	<input id="lawObjectId" name="lawObjectId" type="hidden" value="${lawObjectList.lawObjectId }">
			<%-- 						    <input id="lawObjectId" name="lawObjectId" type="hidden" value="${lawObjectList.id }"> --%>
										<input type="hidden" id ="chickItemList" name ="chickItemList" >
										<%-- <input type="hidden" name="address" value="${localCheak.address }"> --%>
										<input type="hidden" name="objectName" value="${localCheak.objectName }">
										<input type= "hidden" name ="lawObjectType" value="${lawObj.lawObjectType}">
										<input type ="hidden" id="contributionName" name ="contributionName" value ="${ localCheak.contributionName}">
										<input type="hidden" name="saveStatus" id="saveStatus" value="${localCheak.saveStatus}" />
										<input type="hidden" id ="updateObjectState" name="updateObjectState" value ="0">
										<!----↓↓↓↓↓↓↓↓↓↓---->
										<input type="hidden" id="formType" name="formType" value="${localCheak.formType != null ? localCheak.formType : 0}">
										<input type="hidden" id="envSupervisionData" name="envSupervisionData" value="">
										<!------------↑↑↑↑↑↑---->
									<div class="form-group" style="display:none">
										<label for="文书制作单位" class="col-lg-2 col-sm-2 col-xs-5 control-label"><span style="color:red;">*</span>文书制作单位</label>
			                           <div class="col-lg-8 col-sm-8 col-xs-12">
			                               <input readonly type="text" class="form-control" id="makeUnitName" name="makeUnitName" placeholder="文书制作单位" value="${localCheak.makeUnitName }">
			                           </div>
			                        </div>
									<div class="form-group">
										<label class="col-lg-2 col-sm-2 col-xs-5 control-label"> <span
											style="color: red;">*</span> 执法对象名称
										</label>
										<div class="col-lg-8 col-sm-8 col-xs-12">
                                                    <span>
                                                    ${localCheak.objectName }
													<%-- <button type="button" class="btn btn-info no-shadow"
													onclick="selectSubjectBtn('${lawObjectList.lawObjectId }')">执法对象查看</button> --%>
                                                    </span>
										</div>
									</div>
									<div class="form-group">
										<label class="col-lg-2 col-sm-2 col-xs-5 control-label">
										<span style="color: red;">*</span>详细地址</label>
										<div class="col-lg-8 col-sm-8 col-xs-12">
											<span> <input type="text" class="form-control" style="border: none; outline: medium;" id="address" name ="address" value="${localCheak.address }"></span>
										</div>
									</div>
									<div class="form-group">
										<label class="col-lg-2 col-sm-2 col-xs-5 control-label"> 法人代表</label>
										<div class="col-lg-8 col-sm-8 col-xs-12">
										   <c:choose>
												       <c:when test="${lawObjectList.typeCode==1}">
														<input type="text" name="legalPerson" id="legalPerson" value="${ localCheak.legalPerson}" class="form-control" placeholder="法人代表">
													   </c:when>
												       <c:otherwise>
														<input type="text" name="legalPerson" id="legalPerson" value="${ localCheak.legalPerson}"  class="form-control" placeholder="法人代表">
												       </c:otherwise>
											</c:choose>
										</div>
									</div>
									<%-- <div class="form-group">
										<label class="col-lg-2 control-label">法人身份证号</label>
										<div class="col-lg-8">
											<input type="hbfzrdh" class="form-control" id="legalManIdCard" name="legalManIdCard" value="${localCheak.legalManIdCard}" placeholder="法人身份证号">
										</div>
									</div> --%>
									<div class="form-group">
										<label class="col-lg-2 col-sm-2 col-xs-5 control-label">法人电话</label>
										<div class="col-lg-8 col-sm-8 col-xs-12">
										 <c:choose>
									     	<c:when test="${lawObjectList.typeCode==1}">
											  <input type="text" id="legalPhone" name="legalPhone" value="${ localCheak.legalPhone}" class="form-control" placeholder="法人电话">
													   </c:when>
												   <c:otherwise>
														<input type="text" id="legalPhone" name="legalPhone" class="form-control"   value="${ localCheak.legalPhone}" 	placeholder="法人电话">
												  </c:otherwise>
											</c:choose>
										</div>
									</div>
									<div class="form-group">
										<label for="检查人" class="col-lg-2 col-sm-2 col-xs-5 control-label"><span
											style="color: red;">*</span> 检查人</label>
										<div class="col-lg-8 col-sm-8 col-xs-12">
											<div class="input-group">
														<input type="text" id="checkUserNames" name="checkUserNames" readonly class="form-control" placeholder="检查人" value="${localCheak.checkUserNames}">
														<input id="checkUserIds" name="checkUserIds" value="${localCheak.checkUserIds }" type="hidden">
												<div class="input-group-btn">
													<button type="button" class="btn btn-info no-shadow" id="CheckUserChooseBtn" tabindex="-1" data-toggle="modal" data-remote="${webpath}/localExamine/chick-user-page" data-target="#jcr">检查人添加</button>
												</div>
											</div>
										</div>
									</div>
									<div class="form-group">
										<label for="执法证号" class="col-lg-2 col-sm-2 col-xs-5 control-label"><span
											style="color: red;">*</span> 执法证号</label>
										<div class="col-lg-8 col-sm-8 col-xs-12">
											<c:choose>
												<c:when test="${not empty localCheak.lawEnforcIds }">
													<input type="text" readonly id="lawEnforcIds" name="lawEnforcIds" value="${ localCheak.lawEnforcIds }" class="form-control" placeholder="执法证号">
												</c:when>
												<c:otherwise>
													<input type="text" readonly id="lawEnforcIds" name="lawEnforcIds" value="${ historyTask.lawEnforcIds }" class="form-control" placeholder="执法证号">
												</c:otherwise>
											</c:choose>
										</div>
									</div>
									<div class="form-group">
										<label for="记录人" class="col-lg-2 col-sm-2 col-xs-5 control-label"><span style="color:red;">*</span> 记录人</label>
										<div class="col-lg-8 col-sm-8 col-xs-12">
											<div class="input-group">
												<input type="hidden"  id="recordUserId" name="recordUserId" value="${localCheak.recordUserId }" >
												<input type="text" class="form-control" id="recordUserName" name="recordUserName" value="${localCheak.recordUserName }" readonly placeholder="记录人"/>
												<div class="input-group-btn">
													<button type="button" class="btn btn-info no-shadow"
														tabindex="-1" data-toggle="modal" data-target="#recordUserModal">记录人添加</button>
												</div>
											</div>
										</div>
									</div>
									<div class="form-group">
										<label for="参与人员及其工作单位" class="col-lg-2 col-sm-2 col-xs-5 control-label">参与人员及其工作单位</label>
										<div class="col-lg-8">
											<%-- <input class="form-control" id="participant"  name="participant" placeholder="参与人员及其工作单位" value="${localCheak.participant }"> --%>
											<textarea rows="6" class="form-control" id="participant" name="participant" placeholder="参与人员及其工作单位" >${localCheak.participant }</textarea>
										</div>
								    </div>
									<div></div>
									<!-- 检查人的信息-->
									<div class="form-group">
										<label for="被检查单位现场负责人" class="col-lg-2 col-sm-2 col-xs-5 control-label">被检查单位现场负责人</label>
										<div class="col-lg-8 col-sm-8 col-xs-12">
											<input type="text" value="${ localCheak.localPerson }" id="localPerson" name="localPerson" class="form-control" placeholder="被检查单位现场负责人">
										</div>
									</div>
                                    <%-- <div class="form-group">
                                          <label class="col-lg-2 control-label">环保负责人身份证号</label>
                                          <div class="col-lg-8">
                                          		<input type="hbfzrdh" class="form-control" id="chargeManIdCard" name="chargeManIdCard" value="${localCheak.chargeManIdCard }" placeholder="环保负责人身份证号">
                                          </div>
                                    </div> --%>
									<div class="form-group">
										<label for="现场负责人电话" class="col-lg-2 col-sm-2 col-xs-5 control-label">现场负责人电话</label>
										<div class="col-lg-8 col-sm-8 col-xs-12">
											<input type="text" value="${ localCheak.localPersonPhone }" id="localPersonPhone" name="localPersonPhone" class="form-control" placeholder="现场负责人电话">
										</div>
									</div>
									<div class="form-group">
										<label for="职务" class="col-lg-2 col-sm-2 col-xs-5 control-label">职务</label>
										<div class="col-lg-8 col-sm-8 col-xs-12">
											<input type="text" value="${ localCheak.localPersonJob }" id="localPersonJob" name="localPersonJob" class="form-control" placeholder="职务">
										</div>
									</div>

									<div class="form-group">
										<label for="时间" class="col-lg-2 col-sm-2 col-xs-5 control-label"><span style="color: red;">*</span>检查时间</label>
										<div class="col-lg-3 col-sm-3 col-xs-12">
											<input class="form-control" id="checkStartDate" readonly name="checkStartDateTemp" placeholder="开始时间" value="<fmt:formatDate value='${localCheak.checkStartDate }' pattern='yyyy-MM-dd HH:mm:ss'></fmt:formatDate>">
										</div>
										<label for="至" class="col-lg-2 col-sm-2 col-xs-5 control-label"><span style="color: red;">*</span>至</label>
										<div class="col-lg-3 col-sm-3 col-xs-12">
											<input class="form-control" id="checkEndDate" readonly name="checkEndDateTemp" placeholder="结束时间" value="<fmt:formatDate value='${localCheak.checkEndDate }' pattern='yyyy-MM-dd HH:mm:ss'></fmt:formatDate>">
										</div>
									</div>
									<div class="form-group">
										<label for="告知事项" class="col-lg-2 col-sm-2 col-xs-5 control-label">告知事项</label>
										<div class="col-lg-8 col-sm-8 col-xs-12" style="line-height:2em;">
											我们是<input class="from_gaozhi" id="zfhbj" name="informDeptName" placeholder="默认登录用户所在环保局" value="${localCheak.informDeptName }">
											的行政执法人员，这是我们的执法证件（执法证编号：  <input class="from_gaozhi" style="width:300px;" id="zfzh" name="informLawIds" placeholder="默认检查人执法证号" value="${localCheak.informLawIds }">）
											。请过目确认：<input class="from_gaozhi" id="zfqr1" disabled="disabled">
											今天我们依法进行检查并了解有关情况，你应当配合调查，如实提供材料，不得拒绝、阻碍、隐瞒或者提供虚假情况。如果你认为检查人与本案有利害关系，可能影响公正办案，可以申请回避，并说明理由。请确认： <input class="from_gaozhi" id="zfqr2" disabled="disabled">

										</div>
									</div>


									<div class="form-group">
										<label for="行政检查通知书编号" class="col-lg-2 col-sm-2 col-xs-5 control-label">行政检查通知书编号</label>
										<div class="col-lg-8 col-sm-8 col-xs-12">
											<input type="text" value="${ localCheak.administrativeNoticeNumber }" id="administrativeNoticeNumber" name="administrativeNoticeNumber" class="form-control" placeholder="行政检查通知书编号">
										</div>
									</div>

									<!-- 行政检查通知书附件 -->
									<div class="form-group">
										<label for="filees" class="col-lg-2 col-sm-2 col-xs-5 control-label">行政检查通知书附件</label>
										<div class="col-lg-8 col-sm-8 col-xs-12">
											<div class="input-group" id="attach4">
												<!-- 显示已上传的文件名 -->
												<input type="text" class="form-control" id="itemAttachment4"
													   name="itemAttachment4"
													   value="${localCheak.attachmentFileName}"
													   readonly="readonly">

												<!-- 文件上传输入框 -->
												<input type="file" id="filees" name="filees" style="display: none;" multiple onchange="return validateFileUpload(this)">

												<div class="input-group-btn">
													<button type="button" class="btn btn-primary" onclick="triggerFileUpload()">选择文件</button>
													<button type="button" class="btn btn-success" id="deleteAttachmentBtn" data-toggle="modal" data-target="#attachmentModal">删除附件</button>

												</div>
											</div>
										</div>
									</div>


									<div class="container">
										<h3 class="title">
											<span>检查项</span>
										</h3>
									</div>
									<div class="text-right" style="padding-right: 90px;">
										<button type="button" class="btn btn-info btn-sm" id="envSupervisionBtn">
											"环境监管一件事"表单
										</button>
									</div>
									<%-- "环境监管一件事"表单  start ↓↓↓↓↓	--%>
									<div id="envSupervisionForm" style="display: none;">
										<div class="panel-group" id="checkItemAccordion" role="tablist" aria-multiselectable="true">
											<c:forEach items="${checkItemTree}" var="parentItem" varStatus="parentStatus">
												<div class="panel panel-default">
													<!-- 一级菜单标题 -->
													<div class="panel-heading" role="tab" id="heading${parentStatus.index}">
														<h4 class="panel-title">
															<a role="button" data-toggle="collapse" data-parent="#checkItemAccordion"
															   href="#collapse${parentStatus.index}" aria-expanded="false"
															   aria-controls="collapse${parentStatus.index}">
																${parentItem.itemName}
																<i class="fa fa-chevron-down collapse-icon"></i>
															</a>
															<!-- 不涉及单选框，默认隐藏，只在展开时显示 -->
															<div class="not-involved-checkbox" id="notInvolved${parentStatus.index}" style="display: none;">
																<label class="radio-inline">
																	<input type="radio" id="notInvolvedCheck${parentStatus.index}"
																		   name="notInvolvedCheck${parentStatus.index}"
																		   onchange="handleNotInvolvedChange(${parentStatus.index})">
																	<span class="checkbox-text">不涉及</span>
																</label>
															</div>
														</h4>
													</div>

													<!-- 二级表格内容 -->
													<div id="collapse${parentStatus.index}" class="panel-collapse collapse"
														 role="tabpanel" aria-labelledby="heading${parentStatus.index}">
														<div class="panel-body">
															<div class="table-responsive">
																<table class="table table-bordered table-hover">
																	<thead class="bg-light-grey">
																		<tr>
																			<th width="40%" class="text-center">检查要点</th>
																			<th width="30%" class="text-center">存在问题</th>
																			<th width="30%" class="text-center">问题简述</th>
																		</tr>
																	</thead>
																	<tbody>
																		<c:forEach items="${parentItem.children}" var="childItem" varStatus="childStatus">
																			<tr>
																				<td class="text-left">${childItem.itemName}</td>
																				<td class="text-center">
																					<div class="radio-inline">
																						<label class="radio-inline">
																							<input type="radio" name="problem_${parentStatus.index}_${childStatus.index}" value="1" checked>
																							是
																						</label>
																						<label class="radio-inline">
																							<input type="radio" name="problem_${parentStatus.index}_${childStatus.index}" value="0">
																							否
																						</label>
																						<label class="radio-inline">
																							<input type="radio" name="problem_${parentStatus.index}_${childStatus.index}" value="2">
																							不涉及
																						</label>
																					</div>
																				</td>
																				<td class="text-center">
																					<button type="button" class="btn btn-link btn-sm"
																							data-toggle="modal" data-target="#problemDescModal"
																							onclick="openProblemDesc('${parentStatus.index}_${childStatus.index}', event)">
																						<i class="fa fa-ellipsis-h" style="color: #23b7e5;"></i>
																					</button>
																				</td>
																			</tr>
																		</c:forEach>
																	</tbody>
																</table>
															</div>
														</div>
													</div>
												</div>
											</c:forEach>
										</div>
									</div>
									<%-- "环境监管一件事"表单  end ↑↑↑↑↑↑↑	--%>

									<div id="checkItemListContainer" class="smart-widget-body padding-sm">
										<div class="row"  style="word-break: break-all; work-wrap: break-word;">
											<div class="col-md-1"></div>
											<div class="col-md-10">




												<ul id="localChickItemTable"  class="Sortable_xwbl_list">
													<li  v-for="(item, index) in items" :id ="'tr'+index" style="position:relative;">
														<input type="hidden" class="hiddenIndex" :value="index">
														<div style="width:34%; display:inline-block;">
                                                    <span   :id ="'contentTr'+index">
                                                          <span v-if="item.isMust ==1">
                                                             <span style="color: red;">*</span> {{ item.checkItemName }}
                                                          </span>
                                                          <span v-else> &nbsp; &nbsp;{{ item.checkItemName }}
                                                          </span>
                                                    </span>

															<span style="display: none;">{{ item.id }} </span>
														</div>
														<div style="padding-right:100px;display:inline-block; text-align:right; width:65%; vertical-align:middle;">
                                                        <span v-if="item.checkItemStatus ==0" style="margin-top:-15px;">
                                                        <!-- 单选  -->
                                                            <label class="radio-inline" v-if="item.checkItemResult == '1'">
                                                             <input type="radio"  :name="'radio'+index"   checked  v-model="item.checkItemResult"  value="1">
                                                                	 是
                                                            </label>
                                                            <label class="radio-inline" v-else>
                                                            <input type="radio"  :name="'radio'+index"  value="1"  v-model="item.checkItemResult">
                                                             	    是
                                                            </label>
                                                            <c:choose>
																<c:when test="${lawObj.taskFromType =='1' or lawObj.taskFromType =='5'}">
                                                                    <label class="radio-inline" v-if="item.checkItemResult == '0'">
                                                                    <input type="radio"  :name="'radio'+index"    checked  value="0" v-model="item.checkItemResult">
                                                                      	   否</label>
																	<label class="radio-inline" v-else>
                                                                    <input type="radio"  :name="'radio'+index"    v-model="item.checkItemResult" value="0" >
                                                                     	    否
                                                                    </label>
																</c:when>
																<c:otherwise>
                                                                    <label class="radio-inline" v-if="item.checkItemResult == '0'">
                                                                    <input type="radio"  :name="'radio'+index"
																		   checked  value="0" v-model="item.checkItemResult">
                                                                       	  否</label>
																	<label class="radio-inline" v-else>
                                                                    <input type="radio"  :name="'radio'+index"   v-model="item.checkItemResult"
																		   value="0" >
                                                                      	   否
                                                                    </label>
																</c:otherwise>
															</c:choose>
                                                        </span>

															<span v-if="item.checkItemStatus ==4" class="numberWord">
                                                            <!-- 4输入(单行文本) -->
                                                            <input type="text"  class="form-control" maxlength="100"    @input="descInput($event,index)"  :value="item.checkItemResult" v-model="item.checkItemResult">
                                                            <span :id="'remnant'+index" ></span>
                                                        </span>
															<div v-if="item.checkItemStatus ==5">
																<!-- 5经纬度 -->
																<div class="col-lg-12">
																	<div class="col-lg-4" :id="'myDropdown'+index" style="float:right; width:150px; margin-right:-10px;">
																		<button class="btn btn-info" type="button" v-on:click="createMapClick('','','',index)">定位</button>
																		<a class="btn btn-info"  v-on:click="openMobilePlaceBtn(index)" style="cursor:pointer;background:#23b7e5;" data-toggle="dropdown">手机定位</a>
																		<ul class="dropdown-menu" role="menu" style="float:right; padding:5px;" data-stopPropagation="true">
																			<li data-stopPropagation="true">请使用手机App的扫一扫功能<br>扫描以下二维码：<button v-on:click="refreshMobilePlaceBtn(event,index)"  type='button' class='btn btn-danger btn-xs'>刷新</button></li>
																			<li data-stopPropagation="true"><div :id="'appQrCode'+index"  v-on:click="appQrCode(index)" style=' width:230px; height:235px;padding-top:7px;'></div></li>
																		</ul>
																	</div>
																	<div class="col-lg-4" style="float:right;">
																		<input class="form-control" :name="'gisCoordinateY'+index" v-model="item.gisCoordinateY"  disabled="disabled" :id="'mapWD'+index"  placeholder="纬度">
																	</div>
																	<div class="col-lg-3" style="float:right;">
																		<input class="form-control" :name="'gisCoordinateX'+index" v-model="item.gisCoordinateX"  disabled="disabled" :id="'mapJD'+index" placeholder="经度">
																	</div>
																</div>
															</div>
															<div v-if="item.checkItemStatus ==6" style="margin-right:-5px;">
																<!-- 6单一时间-->
																<div class="col-lg-5" style="float:right;">
																	<input type="text" v-model="item.checkItemResult"
																		   :id="'chickOneDate'+index" class="form-control"
																		   readonly="readonly" placeholder="请输入时间"
																		   v-on:click="chickOneDate(index,item.dateType)">
																</div>
															</div>
															<div class ="form-group"  v-if="item.checkItemStatus ==7" style="margin-right:-5px;">
																<!--7时间段 -->
																<div class="col-lg-5" style="float:right;">
																	<input type="text" v-model="item.endDateStr"
																		   :id="'chickTwoEndDate'+index" class="form-control"
																		   readonly="readonly" placeholder="请输入结束时间"
																		   v-on:click="chickTwoEndDate(index,item.dateType)">
																</div>
																<div class="col-lg-5" style="float:right;">
																	<input type="text" v-model="item.startDateStr"
																		   :id="'chickTwoStartDate'+index" class="form-control"
																		   readonly="readonly" placeholder="请输入开始时间"
																		   v-on:click="chickTwoStartDate(index,item.dateType)">
																</div>
															</div>
															<span v-if="item.checkItemStatus ==3" >
                                                            <!--下拉-->
                                                            <span v-if="item.sceneCheckEntryList!= null">
                                                                <select class="form-control" v-model="item.checkItemResult" >
                                                                    <option value="null" >请选择</option>
                                                                    <option  v-for="(tcList1, index) in item.sceneCheckEntryList" :value="tcList1.checkEntryName" >
                                                                    {{tcList1.checkEntryName}}
                                                                    </option>
                                                                </select>
                                                            </span>
                                                        </span>
															<span v-if="item.checkItemStatus ==2" class="numberWord">
                                                            <input type="hidden" name="zxjcInput222" :value="index">
																<!-- 输入-->
                                                            <textarea  class="form-control " @input="descInput2($event,index)"  maxlength="2000" :name="'textarea'+index"  rows="5" v-model="item.checkItemResult" :placeholder="item.checkItemName"></textarea>
                                                            <span :id="'remnant'+index"></span>
                                                        </span>


															<div v-if="item.checkItemStatus ==1">
																<!-- 多选 -->
																<div v-if="item.sceneCheckEntryList!= null" style="float: left;">
																	<div v-for="(tcList,index1) in item.sceneCheckEntryList" class="custom-checkbox" style="width:120px;">
																		<input type="checkbox" :id="'box'+tcList.id+index"  v-model='item.checkboxResult[index1]'  style="float: left;" >
																		<label class="checkbox-blue" :for="'box'+tcList.id+index" style="float: right;"></label>
																		<span style="float: left; margin-left: 10px;" >{{tcList.checkEntryName}}</span>
																	</div>
																</div>
															</div>



														</div>
														<span style="position:absolute;top:50%; margin-top:-12px;right:20px; width:70px;">
                                                    <span v-on:click="remarkCheckItem(index)" class="text-left" data-toggle="modal" data-target="#beizhu">
                                                        <i title ='备注' class="fa fa-ellipsis-h" style="color: #23b7e5; font-size: 20px;"></i>
                                                        <input  type ="text" :id="'remark'+index" style="display: none;"  v-model="item.remark" :value ='item.remark'>
                                                    </span>
                                                    <c:if test ="${lawObj.taskFromType =='1' or lawObj.taskFromType =='5' }">
                                                        <span class="text-left" style="padding:0 5px;">
                                                          <i title ='删除' class="fa fa-times" v-on:click="deleteCheckItem(index)" style="color: #F00; font-size: 20px;"></i>
                                                        </span>
													</c:if>
                                                   <%--  <c:if test ="${lawObj.taskFromType =='1' or lawObj.taskFromType =='5'}"> --%>
                                                      <span class="text-left">
                                                         <label v-if="item.behId != '' ">
                                                            <label v-if="item.behId != null " style="width:10px; background-color:#f7f7f7;cursor:pointer;" >
                                                             <i class="fa fa-book"  v-on:click="bevguide(index)"  style="color:#23b7e5; font-size:20px;"></i>
                                                            </label>
                                                        </label>
                                                        </span>
                                                    <%-- </c:if> --%>
                                                </span>
													</li>
												</ul>
												<div id="addCheckItemContainer" class="padding-sm" style="text-align:left;">
													<c:if test="${lawObj.taskFromType =='1' or lawObj.taskFromType =='5' }">
														<a href ="#">
															<button type="button" class="btn btn-info btn-sm" id ="saveLocalItemBtn"
																	data-remote="${webpath}/refineTemplate/start-checkitem-page?status=2"
																	tabindex="-1" data-toggle="modal" data-target="#xzjcx">新增检查项</button></a>
													</c:if>
												</div>
											</div>
											<div class="col-md-1"></div>
										</div>
									</div>
									<%-- 									<div class="form-group">
                                                                            <label class="col-lg-2 control-label"><span style="color: red;">*</span>是否发现违法行为？</label>
                                                                            <!-- isIllegalcatCodeList -->
                                                                            <div class="col-lg-8">
                                                                            <c:forEach items="${isIllegalcatCodeList}" var="isIllegalcatCodeList">
                                                                                <c:if test="${'0' eq isIllegalcatCodeList.code or '1' eq isIllegalcatCodeList.code}">
                                                                                <div class="radio inline-block">
                                                                                        <div class="custom-radio m-right-xs">
                                                                                        <input type="radio"id="inlineRadio${isIllegalcatCodeList.code}"
                                                                                            <c:if test="${isIllegalcatCodeList.code== localCheak.isIllegalactCode }">checked</c:if>
                                                                                            name="isIllegalactCode"
                                                                                            value="${isIllegalcatCodeList.code}">
                                                                                        <label for="inlineRadio${isIllegalcatCodeList.code}"></label>
                                                                                    </div>
                                                                                    <div class="inline-block vertical-top" style="margin-top: 5px;">${isIllegalcatCodeList.name}</div>
                                                                               </div>
                                                                               </c:if>
                                                                            </c:forEach>

                                                                             <div class="radio inline-block">
                                                                                    <c:if test="${'2' eq localCheak.isIllegalactCode }">
                                                                                        <div class="custom-radio m-right-xs">
                                                                                                <input type="radio"id="inlineRadio${localCheak.isIllegalactCode}"
                                                                                                checked  name="isIllegalactCode"
                                                                                                value="${localCheak.isIllegalactCode}">
                                                                                            <label for="inlineRadio${localCheak.isIllegalactCode}"></label>
                                                                                        </div>
                                                                                        <div class="inline-block vertical-top" style="margin-top: 5px;">${localCheak.isIllegalactName}</div>
                                                                                    </c:if>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <div class="form-group">
                                                                            <label class="col-lg-2 col-sm-2 col-xs-5 control-label"><span style="color: red;">*</span>监察小结</label>
                                                                            <div class="col-lg-8 col-sm-8 col-xs-12">
                                                                                <textarea id="checkSummary" name="checkSummary" class="form-control" rows="5">${ localCheak.checkSummary }</textarea>
                                                                            </div>
                                                                        </div> --%>
								</form>
							</div>
                                <div class="modal-footer" style="margin-right:100px">
										<input type="hidden" name="mainRecordId" id="mainRecordId" value="${localCheak.id}" />
										<input type="hidden" id="itemType" value="localCheck" />
										<button type="button" id="submitLocalExamineForm" class="btn btn-info" >保存</button>
										<c:if test="${localCheak.id!=null  and localCheak.id !='' and  localCheak.saveStatus =='1' }">
											<button type="button" class="btn btn-info"
												onclick="printBtn()" title="手机端不可用" id="printBtn">打印</button>
											<button type="button" class="btn btn-info" id="booksDown">下载文书</button>
											<button type="button" class="btn btn-info"
												id ="shareBooksBtn">分享文书</button>
											<button type="button" class="btn btn-info"
												data-toggle="modal" data-target="#cksmj"
												data-remote="${webpath}/localExamine/cksmj?localCheckId=${localCheak.id}&lsFlag=1">查看扫描件</button>
											<button type="button" class="btn btn-info"
												data-toggle="modal" data-target="#scsmj"
												data-remote="${webpath}/localExamine/scsmj">上传扫描件</button>
											<c:if test="${localCheak.id!=null  and localCheak.id !=''  and (lawObj.taskFromType =='1' or lawObj.taskFromType =='5')}">
											<button type="button" class="btn btn-info" data-toggle="modal" data-target="#cwzdymb">存为自定义模板</button>
											</c:if>
										</c:if>
								</div>
						</div>
				</div>
			</div>
			<!--第三层任务办理row-->
		</div>
	</div>
	</div>
	<!-- 系统推荐模板（Modal） -->
	<div class="modal fade" id="xttjmb" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
			</div>
		</div>
	</div>
	<!-- 系统推荐模板（Modal） -->
	<!-- 查看扫描件（Modal） -->
	<div class="modal fade" id="cksmj" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content"></div>
		</div>
	</div>
	<!-- ./查看扫描件（Modal） -->
	    <a href="#" class="scroll-to-top hidden-print"><i class="fa fa-chevron-up fa-lg"></i></a>
	  <!--右侧悬浮-->
        <div id="leftsead">
        	<c:if test="${lawObj.taskFromType !='1' and lawObj.taskFromType !='5'}">
            <ul>
                <%-- <li><a data-toggle="modal" data-target="#jcxtbsm"><img src="${webpath }/static/img/FormExplain.png" width="131" height="49" class="hides"/><img src="${webpath }/static/img/l02.png" width="45" height="49" class="shows" /></a></li> --%>
               <c:choose>
               	<c:when test="${localCheak.id!=null  and localCheak.id !='' and localCheak.saveStatus !='1' }">
               	   <li  id ="tempSaveClick" ><a href="#"><img id="tempSaveClick" src="${webpath }/static/img/InfoSave.png" width="131" height="49" class="hides"/><img src="${webpath }/static/img/l01.png" width="45" height="49" class="shows" /></a></li>
               	</c:when>
               <c:when test="${localCheak.id==null  or localCheak.id =='' }">
                <li id="tempSaveClick" ><a href="#"><img id="tempSaveClick" src="${webpath }/static/img/InfoSave.png" width="131" height="49" class="hides"/><img src="${webpath }/static/img/l01.png" width="45" height="49" class="shows" /></a></li>
               </c:when>
               </c:choose>
            </ul>
            </c:if>
        </div>
		<!--右侧悬浮JS-->
		<script type="text/javascript">
			$(document).ready(function(){
				$("#leftsead a").hover(function(){
					if($(this).prop("className")=="youhui"){
						$(this).children("img.hides").show();
					}else{
						$(this).children("img.hides").show();
						$(this).children("img.shows").hide();
						$(this).children("img.hides").animate({marginRight:'0px'},'slow');
					}
				},
				function(){
					if($(this).prop("className")=="youhui"){
						$(this).children("img.hides").hide('slow');
					}else{

						$(this).children("img.hides").animate({marginRight:'-143px'},'slow',function(){$(this).hide();$(this).next("img.shows").show();});
					}
				});
				$("#top_btn").click(function(){if(scroll=="off") return;$("html,body").animate({scrollTop: 0}, 600);});
			});
        </script>
        <!-- 检查项填表说明（Modal） -->
        <div class="modal fade" id="jcxtbsm" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"
                            aria-hidden="true">&times;</button>
                        <h4 class="modal-title" id="myModalLabel">填表说明</h4>
                    </div>
                    <div class="modal-body padding-lg" style="height:500px; padding-top:20px;">
                        <div class="smart-widget-body form-horizontal">
                        	<c:if test ="${lawObj.taskFromType =='2'}">
                        		<!-- 2为 涉VOC专项检查任务 -->
                        		  <p>联系人：陈志辉</p>
		                          <p>联系电话：0591-88367367</p>
		                          <p>传真：0591-83518710</p>
		                          <p>电子邮箱：<EMAIL></p>
                        	</c:if>
                        	<c:if test ="${lawObj.taskFromType =='3'}">
                        	<!-- 3为砖瓦专项检查任务 -->
                        	      <p>联系人：冯云飞</p>
		                          <p>联系电话：0591-88367352</p>
		                          <p>传真：0591-88367368</p>
		                          <p>电子邮箱：<EMAIL></p>
                        	</c:if>
                        	<c:if test ="${lawObj.taskFromType =='6'}">
                        	<!-- 3为砖瓦专项检查任务 -->
                        	      <p>联系人：徐璇、叶力玮</p>
		                          <p>联系电话：0591-88367351</p>
		                          <p>传真： </p>
		                          <p>电子邮箱：<EMAIL></p>
                        	</c:if>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
		<div class="modal fade" id="jcr" tabindex="-1" role="dialog"
		   aria-labelledby="fileModalLabel" aria-hidden="true">
			   <div class="modal-dialog  modal-lg">
		        <div class="modal-content">
		        </div>
		      </div>
		</div>
	<!-- ./检查人选择（Modal）  fileModel-->
	<!-- 二级预览模态框（Modal） -->
	<div class="modal fade" id="inputImgModeler" tabindex="-1"
		role="dialog" aria-labelledby="fileModalLabel" aria-hidden="true">
		<div class="modal-dialog  modal-lg">
			<div class="modal-content"></div>
		</div>
	</div>
	<!-- ./二级预览模态框（Modal） -->
	<!-- wfxwzd 违法指导模态框-->
	<div class="modal fade" id="wfxwzd" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content"></div>
		</div>
	</div>
	<!-- wfxwzd 违法指导模态框-->
	<!-- 上传扫描件（modal） -->
	<div class="modal fade" id="scsmj" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content"></div>
		</div>
	</div>
	<!-- /上传扫描件（modal） -->
 <!-- 存为自定义模板（Modal） -->
    <div class="modal fade" id="cwzdymb" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <div style="float:right; margin-top:-5px;">
                    <button type="button" class="btn btn-info" id ="saveSceneBtn"  data-dismiss="modal">保存</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    </div>
                    <h4 class="modal-title" id="myModalLabel">存为自定义模板 </h4>
                </div>
                <form action="#" method="post" id= "templeteForm">
                <input id="templateAreaname" name="templateAreaname" type="hidden" ></input>
				<input id="templateArea" name="templateArea" type="hidden" ></input>
				<input type="hidden" name ="templateObjectType" value ="${lawObj.lawObjectType}">
			    <input type ="hidden" id="templateContributionName" name ="contributionName" value ="${ localCheak.contributionName}">
                <div class="modal-body">
                    <div class="smart-widget-body form-horizontal">
                        <div class="form-group">
                            <label class="col-lg-3 control-label"><span
											style="color: red;">*</span>名称</label>
                            <div class="col-lg-8">
                            <input type="text"  class="form-control" onkeydown="if(event.keyCode==13){return false;}" id="templateName" name ="templateName" placeholder="名称">
                            </div>
                        </div>
                        <div class="form-group">
                                          <label class="col-lg-3 control-label">适用地区</label>
                                               <div class="col-lg-2">
                                                <select
                                                    class="form-control" id ='belongProvince' name ="belongProvince">
                                                    <option value="35000000">福建省</option>
                                                </select>
                                            </div>
                                              <div class="col-lg-3">
                                                <select
                                                    class="form-control" id ="belongCity" name="belongCity">
                                                <!--     <option value="">请选择</option> -->
                                                </select>
                                            </div>
                                              <div class="col-lg-3">
                                                <select
                                                    class="form-control" id ="belongCountry" name ="belongCountry">
                                                </select>
                                            </div>
                                        </div>
                         <div class="form-group">
                            <label class="col-lg-3 control-label">适用对象类型</label>
                            <div class="col-lg-8" style="margin-top:7px;">
                           		<c:choose>
									<c:when test="${lawObj.lawObjectType=='1'}">
									   		企事业单位
										   </c:when>
									<c:when test="${lawObj.lawObjectType=='2'}">
									       	个人
									</c:when>
									<c:when test="${lawObj.lawObjectType=='3'}">
									       个体、三无、小三产
									</c:when>
									<c:when test="${lawObj.lawObjectType=='4'}">
									       	自然保护区
									</c:when>
									<c:when test="${lawObj.lawObjectType=='6'}">
									       	水源地
									</c:when>
									<c:otherwise>
									      	 无主
								 </c:otherwise>
								</c:choose>
                            </div>
                        </div>
                        <c:if test = "${lawObj.lawObjectType=='1'}">
                         <div class="form-group">
                            <input type="hidden" value =" ${lawObjectList.industryTypeCode}" name ="templateIndustry" id ="templateIndustry">
                             <input type="hidden" name ="templateIndustryName" id ="templateIndustryName">
                            <label for="行业类型" class="col-lg-3 col-md-3 control-label">适用行业</label>
                            <div class="col-lg-8" style="margin-top:7px;" >
                         		<span id ="templateIndustryNameTemp">  ${lawObjectList.industryTypeName} </span>
                                <div class="checkbox inline-block" style="padding-right:10px; float:right;">
                                    <div class="custom-checkbox">
                                        <input type="checkbox" id="templateIndustryStatus" name ="templateIndustryStatus">
                                        <label for="templateIndustryStatus" class="checkbox-blue" ></label>
                                    </div>
                                    <div class="inline-block vertical-top">
                                        	所有行业
                                    </div>
                                </div>
                            </div>
                        </div></c:if>
                        <input type="hidden" id ="chickItemListTemp" name ="chickItemListTemp" >
                         <div class="form-group">
                            <label class="col-lg-3 control-label">是否设为常用</label>
                            <div class="col-lg-8">
                                <select class="form-control" id ="usuallyStatus" name ="usuallyStatus">
                                    <option value="">请选择</option>
                                    <option value="1">是</option>
                                    <option value="0">否</option>
                                </select>
                            </div>
                         </div>
                         <div class="form-group">
                            <label class="col-lg-3 control-label">是否设为默认加载模板</label>
                            <div class="col-lg-8">
                                <select class="form-control" name ="defaultStatus">
                                    <option value="">请选择</option>
                                    <option value="1">是</option>
                                    <option value="0">否</option>
                                </select>
                            </div>
                         </div>
                    </div>
                </div>
                </form>
                <div class="modal-footer">
                    <!--<button type="button" class="btn btn-info" id ="saveSceneBtn"  data-dismiss="modal">保存</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>-->
                </div>

            </div>
        </div>
    </div>
    <!-- ./存为自定义模板（Modal） -->
      <!-- 自定义模板（Modal） -->
		<div class="modal fade" id="zdymb" tabindex="-1" role="dialog"
		   aria-labelledby="fileModalLabel" aria-hidden="true">
			   <div class="modal-dialog  modal-lg">
		        <div class="modal-content">
		        </div>
		      </div>
		</div>
	<!-- ./自定义模板（Modal） -->
	<!-- 新增检查项列表（Modal）  -->
	<div class="modal fade" id="xzjcx" tabindex="-1" role="dialog"
		   aria-labelledby="fileModalLabel" aria-hidden="true">
			   <div class="modal-dialog  modal-lg">
		        <div class="modal-content">
		        </div>
		      </div>
		</div>
    <!-- 新增检查项列表（Modal） -->

	 <!-- 行业类型选择（Modal） 模态窗中的模态窗-->
			<div class="modal fade" id="Industrytype" tabindex="-1" role="dialog"
				aria-labelledby="myModalLabel" aria-hidden="true">
				<div class="modal-dialog modal-lg">
					<div class="modal-content">
					</div>
				</div>
			</div>
			<div class="modal fade" id="IndustrytypeModel" tabindex="-1" role="dialog"
				aria-labelledby="myModalLabel" aria-hidden="true">
				<div class="modal-dialog modal-lg">
					<div class="modal-content">
					</div>
				</div>
			</div>
	 <!-- 行业类型选择（Modal） 模态窗中的模态窗-->

    <!-- 新增检查项（Modal） -->
	    <div class="modal fade" id="newChickItem" tabindex="-1" role="dialog"
			aria-labelledby="myModalLabel" aria-hidden="true">
			<div class="modal-dialog modal-lg">
				<div class="modal-content"></div>
			</div>
		</div>
	 <!--地图模态框（Modal） -->
	<div class="modal fade" id="myModal" role="dialog" class="tangram-suggestion-main"
			aria-labelledby="myModalLabel" aria-hidden="true">
			<div class="modal-dialog modal-lg">
				<div class="modal-content">


				</div>
			</div>
		</div>

	<!-- 检查项备注（Modal） -->
	<div class="modal fade" id="beizhu" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal"
						aria-hidden="true">&times;</button>
					<h4 class="modal-title" id="myModalLabel">检查备注说明</h4>
				</div>
				<div class="modal-body">
					<div class="smart-widget-body">
						<form class="form-horizontal">
							<div class="form-group">
								<div class="col-lg-12">
									<label for="exampleInputEmail1">备注说明检查情况及存在的问题</label>
									<textarea class="form-control" id="remarkCheckItemText"
										rows="5"></textarea>
								</div>
								<input type="hidden" id="localCheckitemId" name="localCheckitemId">
								<input type="hidden" id="localCheckitemIndex" name="localCheckitemIndex">
							</div>
						</form>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-info"
						id="remarkCheckItemBtn">确定</button>
					<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
				</div>

			</div>
		</div>
	</div>
	<!-- 分享文书模态框start -->
	<div class="modal fade" id="shareBookModel" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal"
						aria-hidden="true">&times;</button>
					<h4 class="modal-title">分享文书</h4>
				</div>
				<div class="modal-body text-center">
					<div class="pricing-value value" data-toggle="modal"
						data-target="#inputImgModeler"
						style="background-image:url(${webpath }/static/img/QRcode_backgroup.png); width:300px; height:345px; padding:0 auto; margin:0 auto;"
						align="center">
						<div align="center" id="qrcode"></div>
						<!-- <img id="getval"/>  -->
						<div id="getval" align="center"
							style="margin: 0; padding: 110px 0 0 30px;"></div>
						<div
							style="width: 80px; height: 80px; position: absolute; margin: -100px 0 0 125px;">
							<img id="qrCodeIco" src="${webpath }/static/img/qrLogo.png" />
						</div>
					</div>
                    <h4>请微信扫码分享</h4>
				</div>
				<div class="modal-footer">
					<!--<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>-->
				</div>
			</div>
		</div>
	</div>

	<!-- glwfxw关联违法行为 -->
	<div class="modal fade" id="glwfxw" tabindex="-1" role="dialog"
		aria-labelledby="fileModalLabel" aria-hidden="true">
		<div class="modal-dialog  modal-lg">
			<div class="modal-content"></div>
			<!-- /.modal-content -->
		</div>
		<!-- /.modal-dialog -->
	</div>
	<!-- glwfxw关联违法行为 -->

	<!-- 分享文书模态框end -->
	<!-- 模态框（Modal） -->
	<div class="modal fade" id="fileShowModel" tabindex="-1" role="dialog"
		aria-labelledby="fileModalLabel" aria-hidden="true">
		<div class="modal-dialog  modal-lg">
			<div class="modal-content"></div>
			<!-- /.modal-content -->
		</div>
		<!-- /.modal-dialog -->
	</div>
	<!--查看执法对象模态框 start  -->
	<div class="modal fade" id="zfdx" tabindex="-1"
				role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
				<div class="modal-dialog  modal-lg">
					<div class="modal-content"></div>
				</div>
	</div>
	<!--查看执法对象模态框 end  -->
	<!-- 模板预览（Modal） -->
        <div class="modal fade" id="mbyl" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                </div>
            </div>
        </div>
<!-- 模板预览（Modal） -->

	<script type="text/javascript">
/* 	$('.dropdown-menu a.removefromcart').click(function(e) {
	    e.stopPropagation();
		alert("dddddd")
	});
	$(function() {
	    $("ul.dropdown-menu").on("click", "[data-stopPropagation]", function(e) {
	    	alert("ssssssss")
	        e.stopPropagation();
	    });
	}) */
	//执法对象模态框加载
		 function  selectSubjectBtn(lawObjectId){
		  var options  = {
					remote:WEBPATH+'/localExamine/select-law-object-page?lawObjectId='+lawObjectId
				  };
			$('#zfdx').modal(options);
	  	}

		 $(document).ready(function() {
			 //模态窗关闭即移除
			$('#xzjcx').on('hide.bs.modal', function () {
				 $(this).removeData("bs.modal");
			});
			$('#newChickItem').on('hide.bs.modal', function () {
				 $(this).removeData("bs.modal");
			});
		 })

		$(document).ready(function() {
			//记录人模态框特殊校验
			$("#recorderButt").click(function(){
				$("#recordUserName").val(username);
				$("#recordUserId").val(id);
				$('#localExamineForm').formValidation('revalidateField', 'recordUserName');
			});
			$("#recorderCloseButt").click(function(){
				$('#localExamineForm').formValidation('revalidateField', 'recordUserName');
			});

			$("[name='checkSummary']").focus(function(){
				business.listenTextAreaComeEnter();
			})
			$("[name='checkSummary']").blur(function(){
				business.listenTextAreaGoEnter();
			})
			$("#remarkCheckItemText").focus(function(){
				business.listenTextAreaComeEnter();
			})
			$("#remarkCheckItemText").blur(function(){
				business.listenTextAreaGoEnter();
			})
					$("#saveLocalItemBtn").click(function(){
						$("#jcnr").val("");
						$("#relevanceBehaviorIdInput").val("");
						$("#relevanceBehaviorInput").val("");
					});

		//	business.listenEnter('templateName');
					$("#checkStartDate").datetimepicker({
						format : 'yyyy-mm-dd hh:ii:ss',
						language : 'cn',
						endDate : $("#checkEndDate").val(),
						autoclose : true
					}).on('changeDate',function(ev) {
					    $('#localExamineForm').formValidation('revalidateField', 'checkStartDateTemp');
						$("#checkEndDate").datetimepicker('setStartDate',new Date(ev.date.valueOf()));});
					$("#checkEndDate").datetimepicker({
						format : 'yyyy-mm-dd hh:ii:ss',
						language : 'cn',
						startDate : $("#checkStartDate").val(),
						autoclose : true
					}).on('changeDate',function(ev) {
						$('#localExamineForm').formValidation('revalidateField', 'checkEndDateTemp');
						$("#checkStartDate").datetimepicker('setEndDate',new Date(ev.date.valueOf()));});
					$("#relationCaseCode").change(function() {
						$("#relationCaseName").val($("#relationCaseCode option:selected").text());
					});
		/*时间插件特别校验*/
		var dateValidate = function(filedName) {
			$('#localExamineForm').data('formValidation').revalidateField(filedName);
		};
		//表单非空验证
		$("#localExamineForm").formValidation(
						{
							framework : 'bootstrap',
							message : 'This value is not valid',
							icon : {
								valid : 'glyphicon glyphicon-ok',
								invalid : 'glyphicon glyphicon-remove',
								validating : 'glyphicon glyphicon-refresh'
							},
							fields : {
								localPerson : {
									validators : {
										stringLength : {
											max : 50,
											message : '被检查单位现场负责人最大50个字符'
										}
									}
								},
								participant : {
									validators : {
										stringLength : {
											max : 500,
											message : '参与人员及其工作单位最大500个字符'
										}
									}
								},
								legalPhone: {
									 validators: {
					                     // ([0-9]{3,4}-)?[0-9]{7,8} /^0?1[3|4|5|8][0-9]\d{8}$/
					                     regexp: {
					                    	  //regexp: /^(((\d{3,4}-){0,1}\d{7,8})|(1\d{10}))$/,
					                           regexp: /^(((\d{3,4}-{0,1}\d{7,8})|(\d{3,4}){0,1}\d{7,8})|(1\d{10}))$/,
					                    	  message:'请输入正确法人电话',
					                      }
					                 }
								},
								localPersonPhone : {
									 validators: {
					                     // ([0-9]{3,4}-)?[0-9]{7,8} /^0?1[3|4|5|8][0-9]\d{8}$/
					                     regexp: {
					                    	  //regexp: /^(((\d{3,4}-){0,1}\d{7,8})|(1\d{10}))$/,
					                           regexp: /^(((\d{3,4}-{0,1}\d{7,8})|(\d{3,4}){0,1}\d{7,8})|(1\d{10}))$/,
					                    	  message:'请输入正确现场负责人电话',
					                      },
					                 }
								},
								address:{
									validators : {
										notEmpty : {
											message : '详细地址不能为空！'
										},
										stringLength : {
											max : 150,
											message : '详细地址最大150个字符'
										}
									}
								},
								"recordUserName": {
						        	validators: {
						        		notEmpty: {
						                	message: '请选择记录人.'
						                }
						            }
						        },
								localPersonJob : {
									validators : {
										stringLength : {
											max : 100,
											message : '职务最大100个字符'
										}
									}
								},
								checkSummary : {
									validators : {
										notEmpty : {
											message : '监察小结不能为空！'
										},
										stringLength : {
											max : 1300,
											message : '监察小结最大1300个字符'
										}
									}
								},
								checkStartDateTemp : {
									validators : {
										notEmpty : {
											message : '开始时间不能为空！'
										}
									}
								},
								isIllegalactCode : {
									validators : {
										notEmpty : {
											message : '是否存在违法行为不能为空！'
										}
									}
								},
								checkEndDateTemp : {
									validators : {
										notEmpty : {
											message : '结束时间不能为空！'
										}
									}
								},
								makeUnitName:{
									validators : {
										notEmpty : {
											message : '文书制作单位不能为空！'
										},
										stringLength : {
											max : 200,
											message : '文书制作单位最大200个字符'
										}
									}
								},
								checkUserNames : {
									message : '检查人不能为空！',
									validators : {
										notEmpty : {
											message : '检查人不能为空！'
										},
										callback : {
											message : "检查人至少选择在2个，最多选择20个！",
											callback : function(value,
													validator, $field) {
												if (value != "") {
													var checUserIds = $("#checkUserIds").val();
													if (checUserIds != null&& checUserIds != '') {
														var arr = checUserIds.split(",");
														if (arr.length >= 2 && arr.length <= 20) {
															return true;
														} else {
															return false;
														}
													} else {
														return false;
													}
												}
											}
										}
									}
								},
								lawEnforcIds : {
									message : '执法账号不能为空！',
									validators : {
										notEmpty : {
											message : '执法账号不能为空！'
										}
									}
								}
							}
						})
		var localCheakId  =$("#localCheakId").val();
		//保存现场检查表 按钮功能
		$("#submitLocalExamineForm").click(function() {
			//loding('submitLocalExamineForm', '保存');  // 引用Js的加载实现速度太慢
			//debugger;
			document.getElementById('submitLocalExamineForm').innerHTML = "加载.."
			document.getElementById('submitLocalExamineForm').disabled = "disabled"
		    /* setTimeout(function () {
		      document.getElementById('submitLocalExamineForm').innerHTML = '保存';
		      document.getElementById('submitLocalExamineForm').removeAttribute("disabled");
		    },5000); */

			var localCheakId  =$("#localCheakId").val();

			var synchronizationStatus = $("#synchronizationStatus").val();
			var finalVue1 = true;
			if(localCheakId == null || localCheakId ==''){
					 	$("#saveStatus").val("1");
						var text = vue.items;
						var chickItemList = new Array();
						var index =0;
						$(".hiddenIndex").each(function(){
							var i = $(this).val();
							var checkboxList = "";
							if(vue.items[i].isMust=='1'){
								if(vue.items[i].checkItemStatus =='5'){
									//对经纬度存储 转换
									if($("#mapJD"+i).val() =='' ||$("#mapWD"+i).val()==''){
										swal({title:"提示",text:vue.items[i].checkItemName+"为必填信息！",type:"info",allowOutsideClick :true})
										finalVue1 = false;
										return false;
									}
									vue.items[i].checkItemResult =$("#mapJD"+i).val()+","+$("#mapWD"+i).val() ;
								}
							}
							if(vue.items[i].checkItemStatus =='1'){
								//对选字段存储
								var str  = new Array();
								if(vue.items[i].checkboxResult != null && vue.items[i].checkboxResult.length>0){
									for(var j=0;j<vue.items[i].sceneCheckEntryList.length;j++){
										if(vue.items[i].checkboxResult[j]){
											str.push(vue.items[i].sceneCheckEntryList[j].checkEntryName)
										}
									}
									checkboxList =str.join(",");
								}else{
									checkboxList= null;
								}
								vue.items[i].checkItemResult=checkboxList ;
							}else{
								checkboxList =vue.items[i].checkItemResult;
							}
								//必填验证
								if(vue.items[i].isMust=='1'){
									if(vue.items[i].checkItemStatus =='7'){
										if(vue.items[i].startDateStr == null || vue.items[i].startDateStr == ''){
											swal({title:"提示",text:vue.items[i].checkItemName+"为必填信息！",type:"info"})
											finalVue1 = false;
											return false;
										}
										if(vue.items[i].endDateStr == null || vue.items[i].endDateStr == ''){
											swal({title:"提示",text:vue.items[i].checkItemName+"为必填信息！",type:"info",allowOutsideClick :true})
											finalVue1 = false;
											return false;
										}
									}
									if((vue.items[i].checkItemResult == null || vue.items[i].checkItemResult == '' )&& vue.items[i].checkItemStatus !='7'){
											swal({title:"提示",text:vue.items[i].checkItemName+"为必填信息！",type:"info",allowOutsideClick :true})
											finalVue1 = false;
											return false;
										}
									}
							var obj ={checkItemName:vue.items[i].checkItemName,
									checkItemResult:checkboxList,
									modeler_type:1,
									loction:index++,
									template_object_type:lawObjectType,
									sceneItemDatabaseId:vue.items[i].sceneItemDatabaseId,
									checkItemId:vue.items[i].id,
									remark:vue.items[i].remark,
									behId:vue.items[i].behId,
									checkItemStatus:vue.items[i].checkItemStatus,
									checkitemType:vue.items[i].checkitemType,
									isMust:vue.items[i].isMust,
									dateType:vue.items[i].dateType,
									/*startDate:vue.items[i].startDate,*/
									startDateStr:vue.items[i].startDateStr,
									endDateStr:vue.items[i].endDateStr,
						     		/*endDate:vue.items[i].endDate,*/
						     		behFact:vue.items[i].behFact,
						     		sceneSysItemId:vue.items[i].sceneSysItemId
									};
							chickItemList.push(obj);

						})
						$("#chickItemList").val(JSON.stringify(chickItemList));
			}else{
						$("#saveStatus").val("1");
						var text = vue.items;
						var chickItemList = new Array();


						$(".hiddenIndex").each(function(){
							var i = $(this).val();
							//必填验证
							var checkboxList ="";
							if(vue.items[i].checkItemStatus =='5'){
								//对经纬度存储 转换
								if($("#mapJD"+i).val() =='' ||$("#mapWD"+i).val()==''){
									swal({title:"提示",text:vue.items[i].checkItemName+"为必填信息！",type:"info",allowOutsideClick :true})
									finalVue1 = false;
									return false;
								}
								vue.items[i].checkItemResult =$("#mapJD"+i).val()+","+$("#mapWD"+i).val() ;
							}
							if(vue.items[i].checkItemStatus =='1'){
								//对选字段存储
								var str  = new Array();
								if(vue.items[i].checkboxResult != null && vue.items[i].checkboxResult.length>0){
									for(var j=0;j<vue.items[i].sceneCheckEntryList.length;j++){
										if(vue.items[i].checkboxResult[j]){
											str.push(vue.items[i].sceneCheckEntryList[j].checkEntryName)
										}
									}
									checkboxList =str.join(",");
								}else{
									checkboxList= null;
								}
								vue.items[i].checkItemResult=checkboxList ;
							}else{
								checkboxList =vue.items[i].checkItemResult;
							}
							if(vue.items[i].isMust=='1'){
								if(vue.items[i].checkItemStatus =='7'){
									if(vue.items[i].startDateStr == null || vue.items[i].startDateStr == ''){
										swal({title:"提示",text:vue.items[i].checkItemName+"为必填信息！",type:"info",allowOutsideClick :true})
										finalVue1 = false;
										return false;
									}
									if(vue.items[i].endDateStr == null || vue.items[i].endDateStr == ''){
										swal({title:"提示",text:vue.items[i].checkItemName+"为必填信息！",type:"info",allowOutsideClick :true})
										finalVue1 = false;
										return false;
									}
									//continue;
								}
							if((vue.items[i].checkItemResult == null || vue.items[i].checkItemResult == '' )&& vue.items[i].checkItemStatus !='7'){
									swal({title:"提示",text:vue.items[i].checkItemName+"为必填信息！",type:"info",allowOutsideClick :true})
									finalVue1 = false;
									return false;
								}
							}
							var obj ={checkItemName:vue.items[i].checkItemName,
									checkItemResult:checkboxList,
									modeler_type:1,
									templateObjectType:lawObjectType,
									sceneItemDatabaseId:vue.items[i].sceneItemDatabaseId,
									checkItemId:vue.items[i].id,
									behId:vue.items[i].behId,
									remark:vue.items[i].remark,
									checkItemStatus:vue.items[i].checkItemStatus,
									checkitemType:vue.items[i].checkitemType,
									isMust:vue.items[i].isMust,
									id:vue.items[i].id,
									dateType:vue.items[i].dateType,
									loction:index++,
									/*startDate:vue.items[i].startDate,
						     		endDate:vue.items[i].endDate,*/
						     		startDateStr:vue.items[i].startDateStr,
						     		endDateStr:vue.items[i].endDateStr,
						     		behFact:vue.items[i].behFact,
						     		sceneSysItemId:vue.items[i].sceneSysItemId
									};
							chickItemList.push(obj);

						})
						$("#chickItemList").val(JSON.stringify(chickItemList));
				}
			if(finalVue1 == false){
				//现场检查项校验失败
				document.getElementById('submitLocalExamineForm').innerHTML = '保存';
			    document.getElementById('submitLocalExamineForm').removeAttribute("disabled");
				return false;
			}

			//--↓↓↓↓↓↓↓↓↓↓---
			// 检查是否显示环境监管一件事表单，如果显示则收集数据
			if ($('#envSupervisionForm').is(':visible')) {
				// 设置表单类型为环境监管一件事
				$('#formType').val(1);

				// 收集环境监管一件事数据
				var envSupervisionData = collectEnvSupervisionData();
				$('#envSupervisionData').val(JSON.stringify(envSupervisionData));

				console.log('环境监管一件事数据:', envSupervisionData);
			} else {
				// 设置表单类型为原有检查项
				$('#formType').val(0);
				$('#envSupervisionData').val('');
			}
			//----------↑↑↑↑↑↑-----

					$("#localExamineForm").data('formValidation').validate();
					var validate = $("#localExamineForm").data('formValidation').isValid();
					if (validate) {
						//debugger;
						business.openwait();
						var options = {
							url : WEBPATH + '/localExamine/saveLocalExamine',
							type : 'post',
							dataType:"json",
							async: false,
							success : function(data) {
								business.closewait();
								if (data.meta.result == "success") {
									swal({
										title : "提示",
										text : data.meta.message,
										type : "success",
									}, function(isConfirm) {
										business.addMainContentParserHtml(WEBPATH + '/localExamine/xcjc', $("#taskObjectForm").serialize()+ "&selectType=1"+"123456");
									});
									return false;
								}else if(data.meta.code == '007'){
					                swal({ title : data.meta.message, text : "", type : "info",allowOutsideClick :true });
					            } else {
									swal({title:"提示", text:data.meta.message, type:"error",allowOutsideClick :true});
								}

								document.getElementById('submitLocalExamineForm').innerHTML = '保存';
							    document.getElementById('submitLocalExamineForm').removeAttribute("disabled");
							},
							error : function() {
								business.closewait();
								swal({title:"提示 ", text:"保存信息失败!", type:"error",allowOutsideClick :true});
								document.getElementById('submitLocalExamineForm').innerHTML = '保存';
							    document.getElementById('submitLocalExamineForm').removeAttribute("disabled");
							}
						}
						if(synchronizationStatus =='1'){
							  //同步信息
							  $("#updateObjectState").val("1");
						      $('#localExamineForm').ajaxSubmit(options);
						  }else if (synchronizationStatus =='2'){
							  swal({
								  title: "提示?",
								  text: "请确认是否将执法对象信息回写执法对象库!",
								  type: "warning",
								  showCancelButton: true,
								  confirmButtonColor: "#DD6B55",
								  confirmButtonText: "是",
								  cancelButtonText: "否，继续",
								  closeOnConfirm: false,
								  closeOnCancel: false
								},
								function(isConfirm){
								  if (isConfirm) {
									 //会写当事人信息
									  $("#updateObjectState").val("1");
									  $('#localExamineForm').ajaxSubmit(options);
								  }else{
									  //不会写当事人信息
									  $("#updateObjectState").val("0");
									  $('#localExamineForm').ajaxSubmit(options);
								  }
								  })
						  }else{
							  //不同信息
							  $("#updateObjectState").val("0");
							  $('#localExamineForm').ajaxSubmit(options);
						  }
				 	} else if (validate == null) {
						//表单未填写
						$("#localExamineForm").data('formValidation').validate();
						document.getElementById('submitLocalExamineForm').innerHTML = '保存';
					    document.getElementById('submitLocalExamineForm').removeAttribute("disabled");
						return false;
					}else if(validate == false){
						swal({ title : "有必填项未填，请检查", text : "", type : "info",allowOutsideClick :true });
						document.getElementById('submitLocalExamineForm').innerHTML = '保存';
					    document.getElementById('submitLocalExamineForm').removeAttribute("disabled");
					}
				});


		$("#tempSaveClick").click(function(){
				   var text = vue.items;
			       var flag = true;
			       var checkUserIds  = $("#checkUserIds").val();
			       if(checkUserIds==null || checkUserIds ==''){
			    	   flag =false;
			    	   }else{
			    		   if(checkUserIds.split(",").length<2 || checkUserIds.split(",").length>20){
			    			   flag =false;
			    		   }
			    	   }
					//对选字段存储
					var str  = new Array();
					if(localCheakId == null || localCheakId ==''){
					 	$("#saveStatus").val("0");
						var text = vue.items;
						var chickItemList = new Array();
					 	for(var i=0;i<text.length;i++){
						  var checkboxList ="";
								//必填验证
							if(vue.items[i].checkItemStatus =='1'){
								//对选字段存储
								var str  = new Array();
								if(vue.items[i].checkboxResult != null && vue.items[i].checkboxResult.length>0){
									for(var j=0;j<vue.items[i].sceneCheckEntryList.length;j++){
										if(vue.items[i].checkboxResult[j]){
											str.push(vue.items[i].sceneCheckEntryList[j].checkEntryName)
										}
									}
									checkboxList =str.join(",");
								}else{
									checkboxList= null;
								}
								vue.items[i].checkItemResult=checkboxList ;
							}else{
								checkboxList =vue.items[i].checkItemResult;
							}
							if(vue.items[i].isMust=='1'){
								if(vue.items[i].checkItemResult == null || vue.items[i].checkItemResult == '' || vue.items[i].checkItemResult == 'null'){
									flag = false;
								}
							}
							//这个暂存是直接现场执法检查项的属性    直接现场执法没有暂存功能
							var obj ={checkItemName:vue.items[i].checkItemName,
									checkItemResult:checkboxList,
									modeler_type:1,
									template_object_type:lawObjectType,
									sceneItemDatabaseId:vue.items[i].sceneItemDatabaseId,
									loction:i,
									checkItemId:vue.items[i].id,
									remark:vue.items[i].remark,
									behId:vue.items[i].behId,
									checkItemStatus:vue.items[i].checkItemStatus,
									checkitemType:vue.items[i].checkitemType,
									isMust:vue.items[i].isMust,
									dateType:vue.items[i].dateType,
									/*startDate:vue.items[i].startDate,
						     		endDate:vue.items[i].endDate,*/
						     		startDateStr:vue.items[i].startDateStr,
						     		endDateStr:vue.items[i].endDateStr,
						     		behFact:vue.items[i].behFact,
						     		sceneSysItemId:vue.items[i].sceneSysItemId
									};
							chickItemList.push(obj);
						}


						$("#chickItemList").val(JSON.stringify(chickItemList));
					}
					if( localCheakId != null || localCheakId !=''&& ("${lawObj.taskFromType}" !='1' && "${lawObj.taskFromType}" !='5')){
						$("#saveStatus").val("0");
						var text = vue.items;
						var chickItemList = new Array();
						for(var i=0;i<text.length;i++){
							//必填验证
							var checkboxList = "";
							if(vue.items[i].checkItemStatus =='1'){
								//对选字段存储
								var str  = new Array();
								if(vue.items[i].checkboxResult != null && vue.items[i].checkboxResult.length>0){
									for(var j=0;j<vue.items[i].sceneCheckEntryList.length;j++){
										if(vue.items[i].checkboxResult[j]){
											str.push(vue.items[i].sceneCheckEntryList[j].checkEntryName)
										}
									}
									checkboxList =str.join(",");
								}else{
									checkboxList= null;
								}
								vue.items[i].checkItemResult=checkboxList ;
							}else if(vue.items[i].checkItemStatus =='5'){
								if(vue.items[i].gisCoordinateX!=null && vue.items[i].gisCoordinateX!='' && vue.items[i].gisCoordinateY!=null && vue.items[i].gisCoordinateY !=''){
									checkboxList = vue.items[i].gisCoordinateX+','+vue.items[i].gisCoordinateY;
								}else{
									checkboxList = 0+","+0;
								}

							}else{
								checkboxList =vue.items[i].checkItemResult;
							}
							if(vue.items[i].isMust=='1'){
								if(vue.items[i].checkItemResult == null || vue.items[i].checkItemResult == '' || vue.items[i].checkItemResult == 'null'){
									flag = false;
									}
								}
							//这个暂存是专项行动的检查项属性
							//alert(vue.items[i].startDate+"----"+vue.items[i].endDate);
							var obj ={checkItemName:vue.items[i].checkItemName,
									checkItemResult:checkboxList,
									modeler_type:1,
									templateObjectType:lawObjectType,
									sceneItemDatabaseId:vue.items[i].sceneItemDatabaseId,
									loction:i,checkItemId:vue.items[i].id,
									behId:vue.items[i].behId,
									remark:vue.items[i].remark,
									checkItemStatus:vue.items[i].checkItemStatus,
									checkitemType:vue.items[i].checkitemType,
									isMust:vue.items[i].isMust,
									id:vue.items[i].id,
									dateType:vue.items[i].dateType,
									/*startDate:vue.items[i].startDate,
						     		endDate:vue.items[i].endDate,*/
						     		startDateStr:vue.items[i].startDateStr,
									endDateStr:vue.items[i].endDateStr,
						     		behFact:vue.items[i].behFact,
						     		sceneSysItemId:vue.items[i].sceneSysItemId
									};
							chickItemList.push(obj);
						}
						$("#chickItemList").val(JSON.stringify(chickItemList));
					}
					var options = {
							url : WEBPATH + '/localExamine/tempSaveLocalExamine',
							type : 'post',
							dataType:"json",
							success : function(data) {
								if (data.meta.result == "success") {
								 	swal({
										title : "提示",
										text : data.meta.message,
										type : "success",
									}, function(isConfirm) {
										business.addMainContentParserHtml(WEBPATH + '/localExamine/xcjc',
												$("#taskObjectForm").serialize()+ "&selectType=1");
									});
									return false;
								} else if(data.meta.code == '007'){
					                swal({ title : data.meta.message, text : "", type : "info",allowOutsideClick :true });
					            } else {
									swal({title:"提示", text:"暂存信息失败!", type:"error",allowOutsideClick :true});
								}
							},
							error : function() {
								swal({title:"提示 ", text:"暂存信息失败!", type:"error",allowOutsideClick :true});
							}
						}
					if(flag){
						$('#localExamineForm').ajaxSubmit(options);
					}else{
						 swal({
								title : "提示 ",
								text : "必填项未填，是否要暂存？",
								type : "warning",
								showCancelButton : true,
								confirmButtonColor : "#DD6B55",
								confirmButtonText : "是的，暂存",
								cancelButtonText : "让我再考虑一下",
								closeOnConfirm : false,
								closeOnCancel : true
							}, function(isConfirm) {
								if (isConfirm) {
									$('#localExamineForm').ajaxSubmit(options);
								}else {
									swal({
										title : "已取消",
										text : "您取消了操作！",
										type : "info",
										allowOutsideClick :true
									})
								}
							})
					}
		})

		var lawObjectType  = $("#lawObjectType").val();
		var taskId = $("#taskId").val();
			//查询默认检查项信息
			$.ajax({
			cache : true,
			type : "POST",
			url : WEBPATH + '/localExamine/checkItemList',
			data : {
				lawObjectType :  lawObjectType,
				localCheakId:localCheakId,
				status:1,
				taskId:taskId
			},//  form law object
			async : false,
			error : function(request) {
				swal({title:"错误!",text:"获取模板项失败！", type:"error",allowOutsideClick :true});
			},
			success : function(data) {
				$("#templetType").html(data.type);
				dataArr=data.list;
			}
		});

			var uuid = business.guid();//每个页面一个单独的uuid
			//使用系统推荐模板
			$("#sysTemplateBtn").click(function(){
				$.ajax({
					cache : true,
					type : "POST",
					url : WEBPATH + '/localExamine/checkItemList',
					data : {
						lawObjectType :  lawObjectType,
						localCheakId:localCheakId,
						status:0
					},//  form law object
					error : function(request) {
						swal({title:"错误!",text:"获取模板项失败！", type:"error",allowOutsideClick :true});
					},
					success : function(data) {
						$("#contributionName").val("");
						$("#templetType").html(data.type);
						$("#templateContributionName").val("");
						dataArr.splice(0,dataArr.length);
						for(var i =0;i<data.list.length;i++){
						dataArr.push(data.list[i]);
						}
					}
				});
			});

			//设置time为系统时间
			function setSysTime(){
				$.ajax({
		            type: "POST",
		            async:false,//同步获取时间戳
		            url: WEBPATH+'/api/auth/getCurTime',
		            error: function(request) {
		            	swal({title:"提示!",text:"获取系统时间失败，二维码生成失败！", type:"warning",allowOutsideClick :true});
		            	$('#folder').popover('hide');
		            },
		            success: function(data) {
		            	if(data.meta.httpStatusCode ==  '200'){
		            		 time=data.data.curtime;
		            	}else{
		            		swal({title:"提示!",text:"获取系统时间失败，二维码生成失败！", type:"warning",allowOutsideClick :true});
		            		$('#folder').popover('hide');
		            	}
		            }
		        });
			}

			//生成二维码
			function buildQR(time,uuid,index){
				var id ="#appQrCode"+index;
				$(id).empty();
				var QRtext = {appLogo:'CHN-FJ',type:'010',time:time,uuid:uuid,url:'/api/taskManager/appGISInfo'};
				$(id).qrcode({
					render : 'canvas',
					text : JSON.stringify(QRtext),
					height : 225,
					cache:false,
					width : 225,
					foreground: "#23b7e5"
				});
			}
			/*Vue.directive('limitTextLen', {
			    bind: function () {},
			    inserted: function () {},
			    update: function (el, binding) {
			        if (el.value.length >= binding.value) {
			            el.value = el.value.slice(0, binding.value);
			        }
			    },
			    componentUpdated: function () {},
			    unbind: function () {}
			});
			export default {
				  name: 'Box',
				    data() {
				        return {
				            title: '',
				            titleMaxLength: 10,
				        };
				    },
				    methods:{
				    },

				}*/
		var vue = new Vue({
			  el: '#localChickItemTable',
			  data: {
			    items:dataArr
			  }/*,directives: {
			        sliceString: {
			            update(el, binding) {
			                if (el.value.length >= binding.value) {
			                    el.value = el.value.slice(0, binding.value);
			                }
			            }
			        }
			   }*/, methods: {
			        /*sliceString:function() {
			            update(el, binding) {
			                if (el.value.length >= binding.value) {
			                    el.value = el.value.slice(0, binding.value);
			                }
			            }
			        },*/
				   descInput:function(event,index){
					   var a = event.currentTarget.value;
					   var txtVal = a.length;
					   var name = '#remnant'+index;
					   if(100-txtVal>0){
						   $(name).text('还可以输入'+(100 - txtVal)+'个字');
					   }else{
					   		$(name).text('还可以输入0个字');
					   }

					   },
					descInput2:function(event,index){
						   var a = event.currentTarget.value;
						   var txtVal = a.length;
						   var name = '#remnant'+index;
						   if(2000-txtVal>0){
						   		$(name).text('还可以输入'+(2000 - txtVal)+'个字');
						   }else{
							   $(name).text('还可以输入0个字');
						   }
					},
				  openMobilePlaceBtn:function(index){
					  var id ="#appQrCode"+index;
					  setTimeout(function(){
							//$('#folder').popover('hide');
							$(id).empty();
							$(id).html("正在生成二维码……");
							setSysTime();
							buildQR(time,uuid,index);
						},10);
						$("#myDropdown"+index).on('show.bs.dropdown', function () {
							flag = setInterval(function(){
									$.ajax({
						                type: "POST",
						                async:false,
						                url: WEBPATH+'/taskManager/getPositionInfo',
						                data:{uuid:uuid, time:time},
						                error: function(request) {
						                	//swal("错误!","获取位置信息失败！", "error");
						                	//一次请求错误不做处理，等待30分钟后超时
						                },
						                success: function(data) {
						                	if(data.meta.statusCode ==  '200'){
						                		 var position = data.data;
						                		 if(position != null && position != '' && position != 'undefined'){
							                		 var gisCoordinateX = position.split(',')[0];
							                		 var gisCoordinateY = position.split(',')[1];
							                		 var x ="[name='gisCoordinateX"+index+"']";
							                		 var y ="[name='gisCoordinateY"+index+"']";
							                		 vue.items[index].gisCoordinateX =gisCoordinateX;
							                		 vue.items[index].gisCoordinateY =gisCoordinateY;
								                	$("#myDropdown"+index).trigger("click");
							                		 time='';
						                		 }
						                	}else{
						                		//swal("错误!","获取位置信息失败！", "error");
						                		//一次请求错误不做处理，等待30分钟后超时
						                	}
						                }
						            });
								},2000);
							})
							$("#myDropdown"+index).on('hide.bs.dropdown', function () {
								 clearInterval(flag);
							})
				  },
				  appQrCode:function(index){
						var id ="#appQrCode"+index;
						setSysTime();
						buildQR(time,uuid,index);
				  },
				  refreshMobilePlaceBtn:function(e,index){
						e.stopPropagation();
						setSysTime();
						buildQR(time,uuid,index);
				  },
			 	// 百度 地图
				 createMapClick:function(address,gisCoordinateX,gisCoordinateY,index){
						 var urlBaidu = encodeURI(WEBPATH+'/refineTemplate/baidu-ditu?address='+address+'&gisCoordinateX='+gisCoordinateX+'&gisCoordinateY='+gisCoordinateY+"&index="+index);
						  var options = {
									remote:urlBaidu
						  };
						 $('#myModal').modal(options);
					},
				  chickOneDate:function(index,dateType){
					//加载单行时间插件
					if(dateType =="1"){
						//精确到分钟
						 var chickOneDate ="#chickOneDate"+index;
						  $(chickOneDate).datetimepicker({
							    format:'yyyy-mm-dd hh:ii:ss',
								todayBtn : true,
								//clearBtn:true,
								language: 'cn',
								autoclose : true,
							}).on('changeDate',function(ev){
								var date = ev.date;
				                var y = date.getFullYear();
				                var m = date.getMonth() + 1;
				                var d = date.getDate();
				                var h = date.getHours();
				                var i = date.getMinutes();
				                var s = date.getSeconds();
				                vue.items[index].checkItemResult=y + '-' +m + '-' + d +" "+h+":"+i+":"+s;
							});
						  $(chickOneDate).datetimepicker('show');
					}else{
						//精确到日期
						 var chickOneDate ="#chickOneDate"+index;
						  $(chickOneDate).datetimepicker({
							  format:'yyyy-mm-dd',
								todayBtn : true,
								//clearBtn:true,
								language: 'cn',
								autoclose : true,
								weekStart: 1,
							    startView: 2,
							    minView: 2,
							    forceParse: false,
							}).on('changeDate',function(ev){
								var date = ev.date;
				                var y = date.getFullYear();
				                var m = date.getMonth() + 1;
				                var d = date.getDate();
				                vue.items[index].checkItemResult=y + '-' +m + '-' + d;
							});
						  $(chickOneDate).datetimepicker('show');
					}
				  },
				  chickTwoStartDate:function(index,dateType){
					  //时间段开始时间
					  if(dateType =="1"){
						//精确到分钟
							 var chickTwoStartDate ="#chickTwoStartDate"+index;
					  	      $(chickTwoStartDate).datetimepicker({
								    format:'yyyy-mm-dd hh:ii:ss',
									todayBtn : true,
									//clearBtn:true,
									language: 'cn',
									autoclose : true,
									endDate :  vue.items[index].endDateStr
								}).on('changeDate',function(ev){
									var date = ev.date;
					                var y = date.getFullYear();
					                var m = date.getMonth() + 1;
					                var d = date.getDate();
					                var h = date.getHours();
					                var i = date.getMinutes();
					                var s = date.getSeconds();
					                vue.items[index].startDateStr=y + '-' +m + '-' + d +" "+h+":"+i+":"+s;
								});
							  $(chickTwoStartDate).datetimepicker('show');
					  	      $(chickTwoStartDate).datetimepicker('setEndDate', vue.items[index].endDateStr);
					  }else{
						//精确到日期
						  var chickTwoStartDate ="#chickTwoStartDate"+index;
						  $(chickTwoStartDate).datetimepicker({
							  format:'yyyy-mm-dd',
								todayBtn : true,
								//clearBtn:true,
								language: 'cn',
								autoclose : true,
								weekStart: 1,
							    startView: 2,
							    minView: 2,
							    forceParse: false,
							    endDate : vue.items[index].endDateStr
							}).on('changeDate',function(ev){
								var date = ev.date;
				                var y = date.getFullYear();
				                var m = date.getMonth() + 1;
				                var d = date.getDate();
				                vue.items[index].startDateStr=y + '-' +m + '-' + d;
							});
						  $(chickTwoStartDate).datetimepicker('show');
						  $(chickTwoStartDate).datetimepicker('setEndDate', vue.items[index].endDateStr);
					  }
				  },
				  chickTwoEndDate:function(index,dateType){
					  //时间段结束时间
					  if(dateType =="1"){
							//精确到分钟
								 var chickTwoEndDate ="#chickTwoEndDate"+index;
								  $(chickTwoEndDate).datetimepicker({
									    format:'yyyy-mm-dd hh:ii:ss',
										todayBtn : true,
										//clearBtn:true,
										language: 'cn',
										autoclose : true,
										startDate :  vue.items[index].startDateStr
									}).on('changeDate',function(ev){
										var date = ev.date;
						                var y = date.getFullYear();
						                var m = date.getMonth() + 1;
						                var d = date.getDate();
						                var h = date.getHours();
						                var i = date.getMinutes();
						                var s = date.getSeconds();
						                vue.items[index].endDateStr=y + '-' +m + '-' + d +" "+h+":"+i+":"+s;
									});
								  $(chickTwoEndDate).datetimepicker('show');
								  $(chickTwoEndDate).datetimepicker('setStartDate', vue.items[index].startDateStr);
						  }else{
							//精确到日期
							  var chickTwoEndDate ="#chickTwoEndDate"+index;
							  $(chickTwoEndDate).datetimepicker({
								    format:'yyyy-mm-dd',
									todayBtn : true,
									//clearBtn:true,
									language: 'cn',
									autoclose : true,
									weekStart: 1,
								    startView: 2,
								    minView: 2,
								    forceParse: false,
									startDate : vue.items[index].startDateStr
								}).on('changeDate',function(ev){
									var date = ev.date;
					                var y = date.getFullYear();
					                var m = date.getMonth() + 1;
					                var d = date.getDate();
					                vue.items[index].endDateStr=y + '-' +m + '-' + d;
								});
							  $(chickTwoEndDate).datetimepicker('show');
							  $(chickTwoEndDate).datetimepicker('setStartDate', vue.items[index].startDateStr);
						  }
				  },
				  chevronDown:function (contentIdA,contentIdB,index){ // 下移
					  chevronDown(contentIdA,contentIdB,index);

				  },
				  chevronUp:function (contentIdA,contentIdB,index){ // 上移
					  chevronUp(contentIdA,contentIdB,index);
				  },
				  /* 打开备注操作  */
				  remarkCheckItem: function (index) {
					    var remark = "remark".concat(index);
						var remarkTemp = $("#".concat(remark)).val();
						$("#localCheckitemId").val(this.items[index].id);
						$("#localCheckitemIndex").val(index);
						$("#remarkCheckItemText").val(this.items[index].remark);
				    },
				    bevguide:function(index){
				    	  var behId =this.items[index].behId;
				    	  var options = {
									remote:WEBPATH+'/localExamine/behaviorguide?behId='+behId
								  };
								$('#wfxwzd').modal(options);
				    },
				    /* 是否选择操作  */
				      checkItemChooseBtn:function(index) {
						//否操作
						var checkItemResult = this.items[index].checkItemResult;
						if(checkItemResult =='0'){
						var	behId =this.items[index].behId;
							if(behId != null && behId !=''){
							 var options = {
								remote:WEBPATH+'/localExamine/behaviorguide?behId='+behId
							  };
							$('#wfxwzd').modal(options);
							}
						}
					},
				    /* 删除检查项操作   */
				    deleteCheckItem:function(index){
				    var remark = 	this.items[index].remark;
				    var localCheckitemId = this.items[index].id;
				   // if(localCheakId == null || localCheakId =='' ){
				    	//新增操作
				     if(remark == null || remark == ''){
						dataArr.splice(index,1);
				    	swal({
							title :"提示",
							text : "删除成功！",
							type : "success",
							allowOutsideClick :true
						})
				    }else{
				    	 swal({
								title : "提示 ",
								text : "确定要删除所选择的记录吗？",
								type : "warning",
								showCancelButton : true,
								confirmButtonColor : "#DD6B55",
								confirmButtonText : "是的，我要删除！",
								cancelButtonText : "让我再考虑一下",
								closeOnConfirm : false,
								closeOnCancel : false
							}, function(isConfirm) {
								if (isConfirm) {
									dataArr.splice(index,1);
					    			swal({
										title : "删除成功！",
										text : "您已经永久删除了这条信息。",
										type : "success"
									}, function(isConfirm) {
									})
								}else {
									swal({
										title : "已取消",
										text : "您取消了删除操作！",
										type : "info",
										allowOutsideClick :true
									})
								}
							})
				    }
		}
	 }
});
	  //绑定enter监听特殊处理
	  var zxjcIndexs = $("[name='zxjcInput222']");
		for(var i in zxjcIndexs){
			var zxjcIndex = zxjcIndexs[i].value;
			$("[name='textarea"+zxjcIndex+"']").focus(function(){
				business.listenTextAreaComeEnter();
			})
			$("[name='textarea"+zxjcIndex+"']").blur(function(){
				business.listenTextAreaGoEnter();
			})
		}
		// 下移
		function chevronDown(contentIdA,contentIdB,index){
			var vueTop  = vue.items[index];
			var vueDown = vue.items[index+1];
			if(localCheakId!=null && localCheakId!="" ){ // 存在主干id
				//  type 0：上移 1 ：下移
				var obj={contentIdA:contentIdA,contentIdB:contentIdB,index:index,type:1};
				$.ajax({
					cache : true,
					type : "POST",
					url : WEBPATH + '/localExamine/localExamine-save-loction',
					data :obj,
					async : false,
					error : function(request) {
						business.closewait();
						swal({title:"错误!",text:"系统错误", type:"error",allowOutsideClick :true});
					},
					success : function(data) {
						if (data.result == 'success') {// 成功
							Vue.set(vue.items, index, vueDown);
							Vue.set(vue.items, index+1, vueTop);
							var trId = $("#contentTr"+(index+1));
							trId.fadeOut("fast");
							setTimeout(function(){
								trId.fadeIn("fast");
							},100)
						} else if (data.result == 'error') { // 失败
							swal({ title : "移动失败", text : "", type : "error",allowOutsideClick :true });
						} else {
							swal({ title : "返回信息错误", text : "", type : "error",allowOutsideClick :true });
						}
					}
				});
			}else{
				Vue.set(vue.items, index, vueDown);
				Vue.set(vue.items, index+1, vueTop);
				var trId = $("#contentTr"+(index+1));
				trId.fadeOut("fast");
				setTimeout(function(){
					trId.fadeIn("fast");
				},100)
			}
		}
		// 上移
		function chevronUp(contentIdA,contentIdB,index){
			var vueDown  = vue.items[index];
			var vueTop = vue.items[index-1];
			if(localCheakId!=null && localCheakId!="" ){ // 存在主干id
			//  type 0：上移 1 ：下移
				var obj={contentIdA:contentIdA,contentIdB:contentIdB,index:index,type:0};
				$.ajax({
					cache : true,
					type : "POST",
					url : WEBPATH + '/localExamine/localExamine-save-loction',
					data :obj,
					async : false,
					error : function(request) {
						business.closewait();
						swal({title:"错误!",text:"系统错误", type:"error",allowOutsideClick :true});
					},
					success : function(data) {
						if (data.result == 'success') {// 成功
							Vue.set(vue.items, index, vueTop);
							Vue.set(vue.items, index-1, vueDown);

							var trId = $("#contentTr"+(index-1));
							trId.fadeOut("fast");
							setTimeout(function(){
								trId.fadeIn("fast");
							},100)
						} else if (data.result == 'error') { // 失败
							swal({ title : "移动失败", text : "", type : "error",allowOutsideClick :true });
						} else {
							swal({ title : "返回信息错误", text : "", type : "error",allowOutsideClick :true });
						}
					}
				});

			}else{
				Vue.set(vue.items, index, vueTop);
				Vue.set(vue.items, index-1, vueDown);
				var trId = $("#contentTr"+(index-1));
				trId.fadeOut("fast");
				setTimeout(function(){
					trId.fadeIn("fast");
				},100)
			}
		}
//		适用所用行业
		var templateIndustryNameTemp = null;
		var templateIndustryCodeTemp =null;
		$("#templateIndustryStatus").click(function(){
		    if($('input[name="templateIndustryStatus"]').prop("checked"))
	        {
		    	templateIndustryNameTemp = $("#templateIndustryNameTemp").html();
		    	templateIndustryCodeTemp =	$("#templateIndustry").val();
	            $("#templateIndustryNameTemp").html("所有行业");
	            $("#templateIndustry").val("all");
	        }
	        else
	        	$("#templateIndustryNameTemp").html(templateIndustryNameTemp);
		    	$("#templateIndustry").val(templateIndustryCodeTemp);
		});

		//保存自定义模板
	$("#saveSceneBtn").click(function(){
		//获取区划
		var text = vue.items;
		if(text ==null ||  text.length==0){
			swal({title:"提示 ", text:"检查项不能为空，至少含有一条检查项!", type:"error",allowOutsideClick :true});
			return false;
		}
		var belongProvince = $("#belongProvince").val();
		var belongCity =$("#belongCity").val();
		var belongCountry =$("#belongCountry").val();
		if(belongCountry!= null && belongCountry !=''){
			$("#templateArea").val(belongCountry);
			$("#templateAreaname").val($('#belongCountry option:selected').text());
		}else if(belongCountry =='' && belongCity != '' ){
				//选择市
			$("#templateArea").val(belongCity);
			$("#templateAreaname").val($('#belongCity option:selected').text());
		}else{
			$("#templateArea").val('35000000');
			$("#templateAreaname").val("福建省");
		}
		//判断模板的名称不能为空
		var templateName  =$("#templateName").val();
		if(templateName == '' || templateName == null){
			swal({title:"提示 ", text:"模板的名称不能为空!", type:"error",allowOutsideClick :true});
			return false;
		}
		var checkSummary = $("#checkSummary").val();
		if(checkSummary.length>2000){
			swal({title:"提示!",text:"监察小结最大为2000字符！", type:"error",allowOutsideClick :true});
			return false;
		}
		//适用行业名称
	    $("#templateIndustryName").val($("#templateIndustryNameTemp").html());
		var chickItemListTemp = new Array();
		var index =0;
		$(".hiddenIndex").each(function(){
            var i = $(this).val();
        	var obj ={checkItemName:vue.items[i].checkItemName,checkItemResult:0,modeler_type:1,template_object_type:lawObjectType,sceneItemDatabaseId:vue.items[i].sceneItemDatabaseId,loction:index++,checkItemId:vue.items[i].id,behId:vue.items[i].behId,checkItemStatus:vue.items[i].checkItemStatus};
		    //var obj ={checkItemName:vue.items[i].checkItemName,checkItemResult:0,modeler_type:1,template_object_type:lawObjectType,sceneItemDatabaseId:vue.items[i].sceneItemDatabaseId,loction:i,checkItemId:vue.items[i].id};
			chickItemListTemp.push(obj);
		})

	/* 	for(var i=0;i<text.length;i++){

			var obj ={checkItemName:vue.items[i].checkItemName,checkItemResult:0,modeler_type:1,template_object_type:lawObjectType,sceneItemDatabaseId:vue.items[i].sceneItemDatabaseId,loction:i,checkItemId:vue.items[i].id,behId:vue.items[i].behId,checkItemStatus:vue.items[i].checkItemStatus};
		    //var obj ={checkItemName:vue.items[i].checkItemName,checkItemResult:0,modeler_type:1,template_object_type:lawObjectType,sceneItemDatabaseId:vue.items[i].sceneItemDatabaseId,loction:i,checkItemId:vue.items[i].id};
			chickItemListTemp.push(obj);
		} */
		$("#chickItemListTemp").val(JSON.stringify(chickItemListTemp));
			var options = {
				url : WEBPATH + '/localExamine/saveTemplate',
				type : 'post',
				dataType:"json",
				success : function(data) {
					if(data.meta.result =='success'){
						if(data.data.tempStatus == '1'){
						//保存模板和名称重复错误
						swal({title:"提示 ",text:data.meta.message, type:"error",allowOutsideClick :true});
						}else{
						swal({title:"提示 ",text:data.meta.message, type:"success",allowOutsideClick :true});
						$.ajax({
							cache : true,
							type : "POST",
							url : WEBPATH + '/localExamine/checkItemList',
							data : {
								lawObjectType :  lawObjectType,
								localCheakId:localCheakId,
								status:1
							},//  form law object
							async : false,
							error : function(request) {
								swal({title:"错误!",text:"获取模板项失败！", type:"error",allowOutsideClick :true});
							},
							success : function(data) {
								$("#contributionName").val(data.contributionName);
								$("#templateContributionName").val(data.contributionName);
								dataArr=data.list;
							}
						});
						}
					}
				},
				error : function() {
					swal({title:"提示 ", text:"保存模板信息失败!", type:"error",allowOutsideClick :true});
				}
			}
			$('#templeteForm').ajaxSubmit(options);
	});

	$.ajax({
		type:"post",
		url:WEBPATH+"/tArea/chickUserArea",
		dataType:"json",
		async:false,
		data:{},
		success:function(data){
			if(data.cityStatus =='1'){
				//省级用户
				$.ajax({
					type:"post",
					url:WEBPATH+"/tArea/cityList",
					async:false,
					dataType:"json",
					success:function(data){
						$("#belongCity").append("<option value=''>请选择</option>");
						$("#belongCountry").append("<option value=''>请选择</option>");
						$.each(data,function(i,item){
							$("#belongCity").append("<option value="+item.code+">"+item.name+"</option>");
						});
					}
				});
			}else if(data.cityStatus =="2"){
				//市级用户
				$("#belongCity").append("<option selected value="+data.cityCode+">"+data.cityName+"</option>");
				$.ajax({
					type:"post",
					url:WEBPATH+"/tArea/countyListByCode",
					dataType:"json",
					data:{parentCode:data.cityCode},
					success:function(data){
						$("#belongCountry").append("<option value=''>请选择</option>");
						$.each(data,function(i,item){
							$("#belongCountry").append("<option value="+item.code+"  >"+item.name+"</option>");
						});
					}
				});
			}else{
				//县级用户
				$("#belongCity").append("<option selected value="+data.cityCode+">"+data.cityName+"</option>");
				$("#belongCountry").append("<option selected value="+data.countyCode+"  >"+data.countyName+"</option>");
			}
		}
	});
	$("#belongCity").change(function(){
		if ($(this).val() == ""){
			$("#belongCountry option").remove();
			$("#belongCountry").append("<option value=''>请选择</option>");
			return;
		}
		var parentCode = $(this).val();
		$("#belongCountry option").remove();
		$.ajax({
			type:"post",
			url:WEBPATH+"/tArea/countyListByCode",
			async:false,
			dataType:"json",
			data:{parentCode:parentCode},
			success:function(data){
				$("#belongCountry").append("<option value=''>请选择</option>");
				$.each(data,function(i,item){
					$("#belongCountry").append("<option value="+item.code+"  >"+item.name+"</option>");
				});
			}
		});
	});
		//修改检查项备注
		$("#remarkCheckItemBtn").click(function(){
			var localCheckitemId = $("#localCheckitemId").val();
			var localCheckitemIndex = $("#localCheckitemIndex").val();
			var remarkCheckItemText = $("#remarkCheckItemText").val();
			if(remarkCheckItemText.length>2000){
				swal({title:"提示",text:"添加备注信息长度超过最大限制,应在2000个字符内！", type:"error",allowOutsideClick :true});
				return false;
			}
			var textTemp = $("#".concat("remark").concat(localCheckitemIndex)).val();
			if (textTemp != remarkCheckItemText) {
				vue.items[localCheckitemIndex].remark = remarkCheckItemText;
			    $("#remarkCheckItemText").val("");
				$("#localCheckitemId").val("");
				$('#beizhu').modal('hide');
			}
			$('#beizhu').modal('hide');

		})
		//添加检查项方法
		$("#addClickItem").click(function(){
			var jcnr = $("#jcnr").val();
			var taskId = $("#taskId").val();
			if (jcnr == null || jcnr == '' ) {
				swal({title:"提示!", text:"检查内容不能为空!", type:"error",allowOutsideClick :true});
				return;
			}
			if(jcnr.length >500){
				swal({title:"提示!", text:"检查内容最大为500个字符!", type:"error",allowOutsideClick :true});
				return;
			}
			var relevanceBehaviorIdInput = $("#relevanceBehaviorIdInput").val();
			var relevanceBehaviorInput =$("#relevanceBehaviorInput").val();
			if(localCheakId != null && localCheakId != ''  && ('${lawObj.taskFromType}' =='1' || '${lawObj.taskFromType}' =='5')){
			$.ajax({
						cache : true,
						type : "POST",
						url : WEBPATH + '/localExamine/saveCheckItem',
						data : {
							localCheckitem : jcnr,
							taskId : taskId,
							localCheakId:localCheakId,
							templateObjectType:lawObjectType,
							behId:relevanceBehaviorIdInput,
							behFact:relevanceBehaviorInput,
							checkItemStatus:'0'//单选
						},//  form law object
						async : false,
						error : function(request) {
							swal("提示!","检查项操作失败！", "error");
						},
						success : function(data) {
							if (data.meta.result == "success") {
								var obj ={checkItemName:jcnr,checkItemResult:1,remark:'',id:data.data.localCheckitemId,behId:relevanceBehaviorIdInput,behFact:relevanceBehaviorInput,checkItemStatus:'0'};
								//var obj ={checkItemName:jcnr,checkItemResult:1,remark:'',id:data.data.localCheckitemId};
								dataArr.push(obj);
								$("#jcnr").val("");
								$("#localCheckitemId").val("");
								$('#xzjcx').modal('hide')
							}
						}
					})
			}else{
				var obj ={checkItemName:jcnr,checkItemResult:1,remark:'',id:'',behId:relevanceBehaviorIdInput,behFact:relevanceBehaviorInput,checkItemStatus:'0'};
				//var obj ={checkItemName:jcnr,checkItemResult:1,remark:'',id:''};
				dataArr.push(obj);
				$("#jcnr").val("");
				$("#localCheckitemId").val("");
				$('#xzjcx').modal('hide')
			}
		})

		//分享文书
		$("#shareBooksBtn").click(function(){
			var docUrl = $("#docUrl").val();
			var base = new Base64();
			var result = base.encode(FASTDFS_ADDR+docUrl);
			var url = SERVER_ADDR + "qrCodeSharing/sharePage?url="+result;

			if (docUrl != '' && docUrl != null) {
				var aurl = utf16to8(url);

				$('#qrcode').empty();

				$('#qrcode').qrcode({
					render : 'canvas',
					text : aurl,
					height : 180,
					width : 180,
				//logo图片地址${webpath }/static/img/qrLogo.png
				//src: '${webpath }/static/img/qrLogo.png'
				}).hide();

				$("#shareBookModel").modal('show');
			} else {
				swal({
					title : "提示！",
					text : "无可分享的文书！",
					type : "error",
					allowOutsideClick :true
				})
			}

			//从 canvas 提取图片 image
			function convertCanvasToImage(canvas) {
				//新Image对象，可以理解为DOM
				var image = new Image();
				// canvas.toDataURL 返回的是一串Base64编码的URL，当然,浏览器自己肯定支持
				// 指定格式 PNG
				image.src = canvas.toDataURL("image/png");
				return image;
			}

			$('#getval').empty();
			//获取网页中的canvas对象
			var mycanvas1 = document.getElementsByTagName('canvas')[0];
			//将转换后的img标签插入到html中
			var img = convertCanvasToImage(mycanvas1);

			$('#getval').append(img);//imagQrDiv表示你要插入的容器id
			//控制Logo图标的位置
			var margin = ($("#getval").height() - $("#qrCodeIco").height()) / 2;
			$("#qrCodeIco").css("margin", margin);

		});
		if(vue.items != null && vue.items.length>0){
			 for(var i=0;i<vue.items.length;i++){
				 if(vue.items[i].checkItemStatus =='5'){
						//经度纬度绑定值
						$("#mapJD"+i).val(vue.items[i].gisCoordinateX);
						$("#mapWD"+i).val(vue.items[i].gisCoordinateY);
				 }

			 }
		 }
		//下载文书
		$("#booksDown").click(
				function() {
					var localCheckId = $("#localCheakId").val();
					window.location.href = WEBPATH + '/localExamine/booksDown?localCheckId=' + localCheckId;
				});
		})

		function savePic(id,url,tableName,column,fileId){
			var degree = $("#degree").val();
			if(degree!=null && degree!=''&& typeof(degree)!='undefined'){
				if(degree!=0){
					business.openwait();
					$.ajax({
						method:'POST',
						url:WEBPATH+'/askingManager/rotateImage',
						data:{
							id:id,
							url:url,
							tableName:tableName,
							column:column,
							fileId:fileId,
							degree:degree
						}, error: function(request) {
				        	business.closewait();//关闭遮罩层
				        	swal({title:"错误!",text:"系统错误", type:"error",allowOutsideClick :true});
				        },
						success:function(data){
							business.closewait();//关闭遮罩层
							if(data.meta.code==200){
								swal({
									title : "操作成功！",
									text : "",
									type : "success"
								}, function() {
									//刷新当前页面
									//关闭模态框
									$("#inputImgModeler").modal('hide');
									var d = document.getElementById('P'+id);
									d.src = '${FASTDFS_ADDR}'+data.data;
								})
							}else{
								swal({
									title : "操作失败！",
									text : data.meta.message,
									type : "error",
									allowOutsideClick :true
								});
							}
						}
					});


				}else{
					swal({
						title : "操作成功！",
						text : "",
						type : "success",
						allowOutsideClick :true
					});
				}
			}else{
				swal({
					title : "操作成功！",
					text : "",
					type : "success",
					allowOutsideClick :true
				});
				$("#inputImgModeler").modal('hide');
			}

		}

	$(function() {
 			var taskFromType = '${lawObj.taskFromType}';
			if(taskFromType==1 || taskFromType==5){
				$( "#localChickItemTable" ).sortable({
					placeholder: "ui-state-highlight"
				});
				$( "#localChickItemTable" ).disableSelection();
			}
		} );
	 function printBtn(){
		 var multiple = $("#multiple").val();
		 var options = {
					remote:WEBPATH+'/localExamine/print-xckfb-file?localCheckId=${localCheak.id}&multiple='+multiple
				  };
				$('#fileShowModel').modal(options);
	 }
	</script>

<%--行政检查通知书附件上传--%>
	<script>
		$(document).ready(function () {
			// 监听文件选择变化
			$('#filees').on('change', function () {
				var files = $(this)[0].files;
				var fileNames = Array.from(files).map(file => file.name).join(', ');

				// 更新文件选择提示文本
				$('#fileSelectionHint').text(fileNames ? "已选择文件: " + fileNames : "未选择任何文件");
			});

			// 提交上传请求
			$('#submitUpload').on('click', function () {
				var form = $('#uploadForm')[0];
				var formData = new FormData(form);

				business.openwait(); // 加载动画开始

				$.ajax({
					url: WEBPATH + '/localExamine/uploadFile',
					type: 'POST',
					data: formData,
					processData: false,
					contentType: false,
					success: function (response) {
						business.closewait();
						if (response.meta && response.meta.result === "success") {
							swal("提示", "上传成功！", "success");

							// 解析返回的文件名数组
							var fileNames = response.data && response.data.fileName ? response.data.fileName.join(', ') : '';

							// 更新 #fileNamePreview 的内容
							$('#fileNamePreview').text(fileNames || '');

							// 清空文件选择提示文本
							$('#fileSelectionHint').text('');
						} else {
							swal("提示", "上传失败：" + response.meta.message, "error");
						}
					},
					error: function (xhr, status, error) {
						business.closewait();
						swal("提示", "上传失败：" + error, "error");
					}
				});
			});
		});

	</script>


	<script>
		// 触发隐藏的文件选择框
		function triggerFileUpload() {
			$('#filees').click();
		}

		// 监听文件选择变化，更新文本框显示
		$('#filees').on('change', function () {
			var files = $(this).prop("files");
			if (files.length > 0) {
				var fileNames = [];
				$.each(files, function (i, file) {
					fileNames.push(file.name);
				});
				$('#itemAttachment4').val(fileNames.join(', '));
			}
		});

		// 提交上传请求
		$('#submitUpload').on('click', function () {
			var form = $('#uploadForm')[0];
			var formData = new FormData(form);

			business.openwait(); // 加载动画开始

			$.ajax({
				url: WEBPATH + '/localExamine/uploadFile',
				type: 'POST',
				data: formData,
				processData: false,
				contentType: false,
				success: function (response) {
					business.closewait();
					if (response.meta && response.meta.result === "success") {
						swal("提示", "上传成功！", "success");

						// 解析返回的文件名数组
						var uploadedFiles = response.data || [];
						var fileNames = uploadedFiles.map(function (file) {
							return file.fileName;
						}).join(', ');

						// 更新页面上的文件名显示区域
						$('#itemAttachment4').val(fileNames);
						$('#attachmentModal').modal('hide');
					} else {
						swal("提示", "上传失败：" + (response.meta.message || "未知错误"), "error");
					}
				},
				error: function (xhr, status, error) {
					business.closewait();
					swal("提示", "上传失败：" + error, "error");
				}
			});
		});
	</script>




	<script type="text/javascript">
		function uploadFiles() {
			var input = document.getElementById("fileInput");
			var files = input.files;

			if (files.length === 0) {
				alert("请选择要上传的文件");
				return;
			}

			var formData = new FormData();
			for (var i = 0; i < files.length; i++) {
				formData.append("filees", files[i]); // 注意：这里的 "filees" 必须与后端接收参数一致
			}

			business.openwait(); // 显示加载动画

			$.ajax({
				url: WEBPATH + '/localExamine/uploadFile',
				type: 'POST',
				data: formData,
				processData: false,
				contentType: false,
				success: function (response) {
					business.closewait();
					if (response.meta && response.meta.result === "success") {
						swal("提示", "上传成功！", "success");

						var uploadedFiles = response.data || [];
						var fileNames = uploadedFiles.map(function (file) {
							return file.fileName;
						}).join(', ');

						$('#itemAttachment4').val(fileNames);
						$('#attachmentModal').modal('hide');
					} else {
						swal("提示", "上传失败：" + (response.meta.message || "未知错误"), "error");
					}
				},
				error: function (xhr, status, error) {
					business.closewait();
					swal("提示", "上传失败：" + error, "error");
				}
			});
		}
	</script>

	<script>
		function validateFileUpload(fileInput) {
			var allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];
			var file = fileInput.files[0];
			if (file) {
				if (allowedTypes.indexOf(file.type) === -1) {
					swal({title: "提示", text: "仅支持上传 PDF 或 图片格式文件！", type: "error", allowOutsideClick: true});
					fileInput.value = ''; // 清空输入框
					return false;
				}
			}
			return true;
		}

	</script>


	<script type="text/javascript">
		// 删除附件按钮点击事件
		$("#deleteAttachmentBtn").click(function() {
			// 清空显示已上传文件名的输入框
			$("#itemAttachment4").val("");

			// 如果需要，也可以清空隐藏的文件输入框（如果存在）
			$("#filees").val("");

			// 新增：通过 AJAX 或直接提交表单字段，将 localCheak.attachmentFileName 设置为 null
			var localCheakId = $("#localCheakId").val();

			if (localCheakId) {
				$.ajax({
					url: WEBPATH + '/localExamine/deleteFile', // 后端接口地址
					type: 'POST',
					data: {
						localCheakId: localCheakId,
						attachmentFileName: null
					},
					success: function(response) {
						if (response.meta && response.meta.result === "success") {
							swal("提示", "附件信息已清除！", "success");
						} else {
							swal("提示", "更新失败：" + response.meta.message, "error");
						}
					},
					error: function(xhr, status, error) {
						swal("提示", "请求失败：" + error, "error");
					}
				});
			}
		});

	</script>
















<!-- 问题简述模态框 -->
<div class="modal fade" id="problemDescModal" tabindex="-1" role="dialog" aria-labelledby="problemDescModalLabel" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title" id="problemDescModalLabel">问题简述</h4>
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div class="modal-body">
				<textarea id="problemDescText" class="form-control" rows="5" placeholder="请输入问题简述..."></textarea>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
				<button type="button" class="btn btn-primary" onclick="saveProblemDesc()">保存</button>
			</div>
		</div>
	</div>
</div>

<script type="text/javascript">
	// 当前编辑的问题项ID
	var currentProblemId = '';

	// 环境监管一件事表单显示/隐藏控制
	$(document).ready(function() {
		//--↓↓↓↓↓↓↓↓↓↓---
		// 页面初始化时根据FORM_TYPE控制表单展开状态
		var formType = '${localCheak.formType}';
		if (formType === '1') {
			// 如果FORM_TYPE=1，自动展开环境监管一件事表单
			setTimeout(function() {
				$('#envSupervisionBtn').click();
				console.log('页面初始化：自动展开环境监管一件事表单 (FORM_TYPE=' + formType + ')');
			}, 500); // 延迟执行，确保页面完全加载
		}
		//----------↑↑↑↑↑↑-----

		// 添加模态窗口事件监听器用于调试
		// $('#problemDescModal').on('show.bs.modal', function (e) {
		// 	console.log('模态窗口开始显示');
		// });
		//
		// $('#problemDescModal').on('shown.bs.modal', function (e) {
		// 	console.log('模态窗口已完全显示');
		// });
		//
		// $('#problemDescModal').on('hide.bs.modal', function (e) {
		// 	console.log('模态窗口开始隐藏');
		// });
		//
		// $('#problemDescModal').on('hidden.bs.modal', function (e) {
		// 	console.log('模态窗口已完全隐藏');
		// });

		$('#envSupervisionBtn').click(function() {
			var form = $('#envSupervisionForm');
			var addCheckItemContainer = $('#addCheckItemContainer');
			var checkItemListContainer = $('#checkItemListContainer');

			if (form.is(':visible')) {
				// 隐藏表单时：显示「新增检查项」按钮容器和检查项列表内容
				form.slideUp();
				addCheckItemContainer.show();
				checkItemListContainer.show();
				$(this).text('"环境监管一件事"表单');
			} else {
				// 显示表单时：隐藏「新增检查项」按钮容器和检查项列表内容，并展开所有折叠面板
				form.slideDown();
				addCheckItemContainer.hide();
				checkItemListContainer.hide();
				$(this).text('隐藏"环境监管一件事"表单');

				// 展开所有折叠面板
				setTimeout(function() {
					$('#checkItemAccordion .panel-collapse').collapse('show');
				}, 300); // 等待slideDown动画完成后再展开面板
			}
		});

		// 折叠面板图标切换和aria-expanded属性更新
		$('#checkItemAccordion').on('show.bs.collapse', function (e) {
			var $heading = $(e.target).prev('.panel-heading');
			$heading.find('a').attr('aria-expanded', 'true');

			// 显示"不涉及"单选框
			var panelIndex = e.target.id.replace('collapse', '');
			var $notInvolvedDiv = $('#notInvolved' + panelIndex);
			var $titleLink = $heading.find('a');

			$notInvolvedDiv.fadeIn(300);
			$titleLink.addClass('with-checkbox');
		});

		$('#checkItemAccordion').on('hide.bs.collapse', function (e) {
			var $heading = $(e.target).prev('.panel-heading');
			$heading.find('a').attr('aria-expanded', 'false');

			// 隐藏"不涉及"单选框
			var panelIndex = e.target.id.replace('collapse', '');
			var $notInvolvedDiv = $('#notInvolved' + panelIndex);
			var $titleLink = $heading.find('a');

			$notInvolvedDiv.fadeOut(300);
			$titleLink.removeClass('with-checkbox');
		});

		// 为二级检查项的单选按钮添加反向级联事件监听器
		// 使用事件委托，确保动态生成的元素也能正确绑定事件
		$('#checkItemAccordion').on('change', 'input[type="radio"][name^="problem_"]', function() {
			// 从name属性中提取面板索引：problem_0_1 -> 面板索引为0
			var radioName = $(this).attr('name');
			var panelIndex = radioName.split('_')[1];

			// 检查该面板下所有二级检查项的状态，更新一级面板的"不涉及"复选框
			updateParentNotInvolvedStatus(panelIndex);
		});

		// 页面加载完成后，初始化所有面板的"不涉及"复选框状态
		setTimeout(function() {
			// 遍历所有面板，初始化状态
			$('#checkItemAccordion .panel').each(function(index) {
				updateParentNotInvolvedStatus(index);
			});
		}, 500); // 延迟执行，确保DOM完全加载
	});

	// 打开问题简述模态框
	function openProblemDesc(problemId, event) {
		try {
			// 阻止事件冒泡和默认行为
			if (event) {
				event.preventDefault();
				event.stopPropagation();
			}

			console.log('打开问题简述模态框，问题ID:', problemId);

			currentProblemId = problemId;
			var savedDesc = localStorage.getItem('problemDesc_' + problemId) || '';
			$('#problemDescText').val(savedDesc);

			// 确保模态窗口正确显示
			$('#problemDescModal').modal({
				backdrop: 'static',  // 防止点击背景关闭
				keyboard: false      // 防止ESC键关闭
			});
			$('#problemDescModal').modal('show');

			console.log('模态窗口已显示');
		} catch (error) {
			console.error('打开问题简述模态框时发生错误:', error);
		}
	}

	// 保存问题简述
	function saveProblemDesc() {
		try {
			var desc = $('#problemDescText').val();
			if (currentProblemId) {
				localStorage.setItem('problemDesc_' + currentProblemId, desc);
				// 可以在这里添加保存到后端的逻辑
				console.log('保存问题简述:', currentProblemId, desc);

				// 显示保存成功提示
				if (desc.trim()) {
					console.log('问题简述已保存');
				}
			}
			$('#problemDescModal').modal('hide');
		} catch (error) {
			console.error('保存问题简述时发生错误:', error);
		}
	}

	// 处理"不涉及"单选框变化
	function handleNotInvolvedChange(panelIndex) {
		var checkbox = document.getElementById('notInvolvedCheck' + panelIndex);
		var isChecked = checkbox.checked;

		console.log('面板 ' + panelIndex + ' 不涉及状态:', isChecked);

		// 如果选中"不涉及"，将该面板下所有行的"存在问题"列都勾选为「不涉及」选项
		if (isChecked) {
			// 查找该面板下所有的"不涉及"单选按钮（value="2"的选项）
			$('#collapse' + panelIndex + ' input[type="radio"][value="2"]').each(function() {
				$(this).prop('checked', true);
				// 不触发change事件，避免无限循环
			});

			console.log('已将面板 ' + panelIndex + ' 下所有行的"存在问题"设置为"不涉及"');
		}
		// 注意：取消勾选时不自动取消下方表格的选择状态，让用户手动调整

		// 可以在这里添加保存到后端的逻辑
		// saveNotInvolvedStatus(panelIndex, isChecked);
	}

	//--↓↓↓↓↓↓↓↓↓↓---
	// 收集环境监管一件事数据
	function collectEnvSupervisionData() {
		var data = [];

		try {
			// 遍历所有面板
			$('#checkItemAccordion .panel').each(function(panelIndex) {
				var $panel = $(this);
				var $collapse = $panel.find('.panel-collapse');

				// 遍历该面板下的所有检查项行
				$collapse.find('tbody tr').each(function() {
					var $row = $(this);
					var $cells = $row.find('td');

					if ($cells.length >= 3) {
						// 获取检查项信息
						var radioName = $row.find('input[type="radio"]').first().attr('name');
						if (radioName) {
							// 提取configItemId（格式：problem_panelIndex_childIndex）
							var configItemId = radioName.replace('problem_', '');

							var itemName = $cells.eq(0).text().trim();
							var $checkedRadio = $row.find('input[type="radio"]:checked');
							var result = $checkedRadio.length > 0 ? $checkedRadio.val() : '';

							// 获取问题简述
							var problemDesc = localStorage.getItem('problemDesc_' + configItemId) || '';

							// 只收集有选择结果的数据
							if (result) {
								data.push({
									configItemId: configItemId,
									itemName: itemName,
									result: result,
									problemDesc: problemDesc
								});
							}
						}
					}
				});
			});

			console.log('收集到的环境监管一件事数据:', data);
			return data;

		} catch (error) {
			console.error('收集环境监管一件事数据时发生错误:', error);
			return [];
		}
	}

	// 加载环境监管一件事历史数据
	function loadEnvSupervisionHistoryData() {
		var taskId = $('#taskId').val();
		if (!taskId) {
			console.warn('任务ID为空，无法加载历史数据');
			return;
		}

		$.ajax({
			url: WEBPATH + '/localExamine/loadEnvSupervisionData',
			type: 'POST',
			data: { taskId: taskId },
			dataType: 'json',
			success: function(response) {
				if (response.meta.result === 'success' && response.data && response.data.length > 0) {
					console.log('加载到历史数据:', response.data);

					// 恢复表单状态
					response.data.forEach(function(item) {
						// 根据configItemId找到对应的单选按钮并设置选中状态
						var configItemId = item.configItemId;
						if (configItemId) {
							// 查找匹配的单选按钮（configItemId格式为 parentIndex_childIndex）
							var $targetRadio = $('input[type="radio"][name*="problem_"][name*="_' + configItemId + '"][value="' + item.checkItemResult + '"]');
							if ($targetRadio.length > 0) {
								$targetRadio.prop('checked', true);
								console.log('恢复单选按钮状态:', configItemId, '=', item.checkItemResult);
							}

							// 恢复问题简述到localStorage
							if (item.problemDesc) {
								localStorage.setItem('problemDesc_' + configItemId, item.problemDesc);
								console.log('恢复问题简述:', configItemId, '=', item.problemDesc);
							}
						}
					});

					console.log('环境监管一件事历史数据加载完成，共恢复', response.data.length, '条数据');
				} else {
					console.log('没有环境监管一件事历史数据或数据为空');
				}
			},
			error: function(xhr, status, error) {
				console.error('加载环境监管一件事历史数据失败:', error);
			}
		});
	}

	// 页面加载完成后，如果有历史数据则加载
	$(document).ready(function() {
		// 延迟加载历史数据，确保页面完全渲染完成
		setTimeout(function() {
			loadEnvSupervisionHistoryData();
		}, 1000);
	});
	//----------↑↑↑↑↑↑-----

	// 反向级联：根据二级检查项状态更新一级面板的"不涉及"复选框
	function updateParentNotInvolvedStatus(panelIndex) {
		// 获取该面板下所有的单选按钮组
		var $panel = $('#collapse' + panelIndex);
		var $radioGroups = $panel.find('input[type="radio"][name^="problem_' + panelIndex + '_"]');

		// 按name属性分组，获取每个检查项的选中状态
		var radioGroupNames = [];
		$radioGroups.each(function() {
			var name = $(this).attr('name');
			if (radioGroupNames.indexOf(name) === -1) {
				radioGroupNames.push(name);
			}
		});

		var allNotInvolved = true;  // 是否所有项都选择了"不涉及"
		var hasNonNotInvolved = false;  // 是否有项选择了"是"或"否"

		// 检查每个检查项的选中状态
		for (var i = 0; i < radioGroupNames.length; i++) {
			var groupName = radioGroupNames[i];
			var $checkedRadio = $panel.find('input[type="radio"][name="' + groupName + '"]:checked');

			if ($checkedRadio.length > 0) {
				var checkedValue = $checkedRadio.val();
				if (checkedValue !== '2') {  // 不是"不涉及"
					allNotInvolved = false;
					hasNonNotInvolved = true;
				}
			} else {
				// 如果没有选中任何选项，认为不是"不涉及"
				allNotInvolved = false;
			}
		}

		// 更新一级面板的"不涉及"复选框状态
		var $parentCheckbox = $('#notInvolvedCheck' + panelIndex);

		if (allNotInvolved && radioGroupNames.length > 0) {
			// 所有二级项都选择了"不涉及"，自动勾选一级复选框
			$parentCheckbox.prop('checked', true);
			console.log('面板 ' + panelIndex + ' 的所有检查项都选择了"不涉及"，自动勾选一级复选框');
		} else if (hasNonNotInvolved) {
			// 有项选择了"是"或"否"，自动取消勾选一级复选框
			$parentCheckbox.prop('checked', false);
			console.log('面板 ' + panelIndex + ' 有检查项选择了"是"或"否"，自动取消勾选一级复选框');
		}
	}
</script>

</body>

</html>
