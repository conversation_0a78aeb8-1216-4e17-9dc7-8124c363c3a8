package org.changneng.framework.frameworkweb.controller;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.changneng.framework.frameworkbusiness.aop.SysLogPoint;
import org.changneng.framework.frameworkbusiness.aop.SysLogPoint.businessType;
import org.changneng.framework.frameworkbusiness.aop.SysLogPoint.dbType;
import org.changneng.framework.frameworkbusiness.dao.SysDepartmentMapper;
import org.changneng.framework.frameworkbusiness.dao.SysFilesMapper;
import org.changneng.framework.frameworkbusiness.dao.TaskMapper;
import org.changneng.framework.frameworkbusiness.entity.*;
import org.changneng.framework.frameworkbusiness.entity.vo.CheckItemConfigTreeVO;
import org.changneng.framework.frameworkbusiness.repeatCommit.CheckRepeatCommit;
import org.changneng.framework.frameworkbusiness.repeatCommit.CheckRepeatToken;
import org.changneng.framework.frameworkbusiness.repeatCommit.PutSessionValue;
import org.changneng.framework.frameworkbusiness.service.*;
import org.changneng.framework.frameworkcore.exception.BusinessException;
import org.changneng.framework.frameworkcore.utils.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

@Controller
@RequestMapping(value = "/localExamine")
public class LocalExamineController {
	// localExamine/xcjc
	private static Logger logger = LogManager.getLogger();
	@Autowired
	private JcblService jcblService;

	@Autowired
	private TaskFlowService taskFlowService;

	@Autowired
	private ZfdxManagerService zfdxManagerService;

	@Autowired
	private LocalExamineService LocalExamineService;

	@Autowired
	private SysDepartmentMapper sysDepartmentMapper;

	@Autowired
	private RandomPollutionSDBService randomPollutionSDBService;

	@Autowired
	private ISurveyRecordService surveyRecordService;

	@Autowired
	private TaskMapper taskMapper;
	@Autowired
	private GetParticipantService getParticipantService;

	@Autowired
	private SysFilesMapper sysFilesMapper;

	@Autowired
	private CheckItemConfigService checkItemConfigService;

	/**
	 * 初始化页面
	 *
	 * @param model
	 * @param lawObj
	 * @param request
	 * @param response
	 * @param localChickId
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/xcjc", method = RequestMethod.POST)
	@PutSessionValue
	public ModelAndView localExamine(Model model, LawObjectTypeBean lawObj, HttpServletRequest request,
		HttpServletResponse response, @RequestParam(value = "localChickId", required = false) String localChickId,String menuId
	)
			throws Exception {
		ModelAndView mav = null;
		if (lawObj.getTaskId() == null) {
			mav = new ModelAndView("error/500");
			return mav;
		}

		// LawEnforceObjectWithBLOBs lawObjectList = null;
		historyLawEnforceObjectWithBLOBs lawObjectList = null;
		LocalCheck localCheak = null;
		List<LocalCheckItem> localCheckItem = null;
		Task task = null;
		try {
			//查询 “环境执法一件事”的初始化树
			List<CheckItemConfigTreeVO> checkItemTree = checkItemConfigService.getTreeStructure();
			model.addAttribute("checkItemTree", checkItemTree);

			// 执法对象信息
			SysUsers sysUser = (SysUsers) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			taskFlowService.changeLawObjForTaskFlowState(lawObj,sysUser);
			// 执法对象必要传递信息,勘察记录关联的主对象
			// 根据主键的id查询任务信息
			if (!ChangnengUtil.isNull(lawObj.getTaskId())) {
				task = LocalExamineService.selectByTaskId(lawObj.getTaskId());
			}
			// 执法对象必要传递信息,勘察记录关联的主对象
			model.addAttribute("lawObj", lawObj);
			// 查询任务总轴信息
			// List<TaskNode> taskNodeList =
			// taskFlowService.getTaskNodeList(lawObj.getTaskId());
			// model.addAttribute("taskNodeList", taskNodeList);
			if ("0".equals(lawObj.getParentUrl())) { // 历史途径
				lawObjectList = zfdxManagerService.selectByTaskId(lawObj.getTaskId());
				localCheak = LocalExamineService.getLocalCheickItem(lawObj);
				if (localCheak != null && !"".equals(localCheak.getId())) {
					localCheckItem = LocalExamineService.getChickItemItem(lawObj, localCheak.getId());
					if (localCheckItem == null || localCheckItem.size() == 0) {
						model.addAttribute("localCheckItemStatus", "0");
					}
					
					//--↓↓↓↓↓↓↓↓↓↓---
					// 如果是环境监管一件事表单类型，查询对应的历史数据
					if (Integer.valueOf(1).equals(localCheak.getFormType())) {
						try {
							// 加载环境监管一件事检查项历史数据
							List<LocalCheckItem> envSupervisionItems = LocalExamineService.loadEnvSupervisionItems(localCheak.getId());
							// 将历史数据添加到模型中供前端使用
							model.addAttribute("envSupervisionItems", envSupervisionItems);
							logger.info("成功加载历史任务环境监管一件事数据，任务ID: {}, 数据条数: {}", lawObj.getTaskId(), envSupervisionItems.size());
						} catch (Exception e) {
							logger.error("加载历史任务环境监管一件事数据失败，任务ID: {}", lawObj.getTaskId(), e);
						}
					}
					//----------↑↑↑↑↑↑-----
				} else {
					model.addAttribute("localCheckItemStatus", "0");
				}
				model.addAttribute("lawObjectList", lawObjectList);
				model.addAttribute("localCheak", localCheak);
				model.addAttribute("localCheckItem", localCheckItem);
				String doubleAttrNames = randomPollutionSDBService.selectNamesByObjId(task.getLawObjectId());
				model.addAttribute("doubleAttrNames", doubleAttrNames);
				model.addAttribute("menuId",menuId);
				mav = new ModelAndView("taskManager/taskReadOnly/lsrw-xckfb");
			} else {


				// 执法对象查询，当是编辑状态下，需要执法对象模态框关联信
				lawObjectList = zfdxManagerService.selectByTaskId(lawObj.getTaskId());
				String doubleAttrNames = randomPollutionSDBService.selectNamesByObjId(task.getLawObjectId());
				localCheak = LocalExamineService.getLocalCheickItem(lawObj);
				if (localCheak == null) {
					SysDepartment sysDepartment = sysDepartmentMapper.selectFristDepartmentByNumber(
							"D00" + sysUser.getBelongAreaId().substring(0, 6), sysUser.getBelongAreaId());
					localCheak = new LocalCheck();

					localCheak.setFormType(0);

					localCheak.setcheckUserIds(task.getChecUserIds());
					localCheak.setcheckUserNames(task.getChecUserNames());
					localCheak.setLawEnforcIds(task.getLawEnforcIds());
					if (sysDepartment != null) {
						localCheak.setMakeUnitName(sysDepartment.getDepartmentName());
						localCheak.setInformDeptName(sysDepartment.getDepartmentName());
					}
					localCheak.setInformLawIds(task.getLawEnforcIds());
					localCheak.setLawObjectId(lawObjectList.getLawObjectId());
					//
					localCheak.setRecordUserId(sysUser.getId());
					localCheak.setRecordUserName(sysUser.getLoginname());

					if (lawObjectList != null) {
						localCheak.setObjectName(lawObjectList.getObjectName());
						localCheak.setAddress(lawObjectList.getAddress());
						if ("1".equals(lawObjectList.getTypeCode())) {
							localCheak.setLegalPerson(lawObjectList.getLegalPerson());
							// 法人电话
							localCheak.setLegalPhone(lawObjectList.getLegalPhone());
							localCheak.setLocalPerson(lawObjectList.getChargePerson());
							localCheak.setLocalPersonPhone(lawObjectList.getChargePersonPhone());
							//localCheak.setChargeManIdCard(lawObjectList.getChargeManIdCard());

						} else {
							localCheak.setLocalPerson(lawObjectList.getLegalPerson());
							// 法人电话
							localCheak.setLocalPersonPhone(lawObjectList.getLegalPhone());
						}
					}
					if (lawObj.getTaskId() != null) {
						String participant = getParticipantService.getParticipantInfo(lawObj.getTaskId());
						localCheak.setParticipant(participant);
					}
				}
				//V2.0.3新增 初始化 检查开始时间  检查结束时间字段
				if(ChangnengUtil.isNull(localCheak.getCheckStartDate())&&ChangnengUtil.isNull(localCheak.getCheckEndDate())) {
					localCheak.setCheckStartDate(task.getLawEnforcementStartTime()!=null?task.getLawEnforcementStartTime():null);
					localCheak.setCheckEndDate(task.getLawEnforcementEndTime()!=null?task.getLawEnforcementEndTime():null);
				}
				
				//--↓↓↓↓↓↓↓↓↓↓---
				// 如果是环境监管一件事表单类型，查询对应的历史数据
				if (localCheak != null && Integer.valueOf(1).equals(localCheak.getFormType())) {
					try {
						// 加载环境监管一件事检查项历史数据
						List<LocalCheckItem> envSupervisionItems = LocalExamineService.loadEnvSupervisionItems(localCheak.getId());
						// 将历史数据添加到模型中供前端使用
						model.addAttribute("envSupervisionItems", envSupervisionItems);
						logger.info("成功加载环境监管一件事历史数据，任务ID: {}, 数据条数: {}", lawObj.getTaskId(), envSupervisionItems.size());
					} catch (Exception e) {
						logger.error("加载环境监管一件事历史数据失败，任务ID: {}", lawObj.getTaskId(), e);
					}
				}
				//----------↑↑↑↑↑↑-----
				
				// IS_ILLEGALACT_CODE是否存在违法行为
				List<TcDictionary> isIllegalcatCodeList = jcblService.TcDictionaryList("IS_ILLEGALACT_CODE");
				model.addAttribute("localCheak", localCheak);
				model.addAttribute("isIllegalcatCodeList", isIllegalcatCodeList);
				// 执法对象必要传递信息
				// model.addAttribute("checkItemList", checkItemList);
				model.addAttribute("doubleAttrNames", doubleAttrNames);
				model.addAttribute("lawObjectList", lawObjectList);
				mav = new ModelAndView("taskManager/xczf-xckfb");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return mav;
	}

	/**
	 * 保存现场检查表 信息
	 *
	 * @param request
	 * @param response
	 * @param localCheck
	 * @return
	 */
	//@CheckRepeatCommit

	@RequestMapping(value = "/saveLocalExamine", method = RequestMethod.POST)
	@CheckRepeatToken
	@SysLogPoint(businessType = businessType.ADD_LOCAL_CHECK, dbOptType = dbType.ADD)
	@ResponseBody
	public ResponseJson saveLocalExamine(Model model, HttpServletRequest request, HttpServletResponse response,
			@Validated LocalCheck localCheck, BindingResult bResult, String chickItemList, String taskId,
			String checkStartDateTemp, String checkEndDateTemp, Integer formType, String envSupervisionData) throws Exception {
	//----------↑↑↑↑↑↑-----
		// logger.info("现场检查");
		if (!bResult.hasErrors()) {
			String administrativeNoticeNumber = request.getParameter("administrativeNoticeNumber");
//			String administrativeNoticeAttachment = request.getParameter("administrativeNoticeAttachment");
			localCheck.setAdministrativeNoticeNumber(administrativeNoticeNumber);
//			localCheck.setAdministrativeNoticeAttachment(administrativeNoticeAttachment);
			// 手动获取文件

			if (!"".equals(checkEndDateTemp) && checkEndDateTemp != null) {
				// Date checkEndDate =
				// DateUtil.getSimpleFormate(checkEndDateTemp);
				SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
				Date parse = format.parse(checkEndDateTemp);
				localCheck.setCheckEndDate(parse);

			}
			if (!"".equals(checkStartDateTemp) && checkStartDateTemp != null) {
				SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
				Date parse = format.parse(checkStartDateTemp);
				localCheck.setCheckStartDate(parse);
			}
			SysUsers sysUser = (SysUsers) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

				// 记录web登录默认值为0
			localCheck.setIsAppHandle(0);
			// 20171201 王伟定时没有把 询问笔录、现场检查、勘查笔录单位更新，这里在保存的时候重新查询。
			SysDepartment sysDepartment = sysDepartmentMapper.selectFristDepartmentByNumber("D00"+sysUser.getBelongAreaId().substring(0,6),sysUser.getBelongAreaId());
			if (sysDepartment != null) {
				localCheck.setMakeUnitName(sysDepartment.getDepartmentName());
			}

			// 上传文件并获取文件列表
			List<SysFiles> filesList = LocalExamineService.uploadFiless(request, response, sysUser);

			//--↓↓↓↓↓↓↓↓↓↓---
			// 调用扩展的 saveLocalExamine 方法，支持环境监管一件事
			ResponseJson json = LocalExamineService.saveLocalExamine(localCheck, taskId, chickItemList, sysUser, filesList, formType, envSupervisionData);
			//----------↑↑↑↑↑↑-----


//			ResponseJson json = LocalExamineService.saveLocalExamine(localCheck, taskId, chickItemList, sysUser);

			// 执法对象必要传递信息
			// model.addAttribute("lawObj", lawObj);
			// ModelAndView mav = new ModelAndView("taskManager/xczf-xckfb");
			return json;
		} else {
			ResponseJson json = new ResponseJson();
			json.error(HttpStatus.BAD_REQUEST.toString(), SystemStatusCode.PARAM_VALIDATE_FAILURE.toString(), bResult.getFieldError().getDefaultMessage(), null, null);
			return json;
		}
	}

	// tempSaveLocalExamine

	/**
	 * 暂存现场检查表 信息
	 *
	 * @param request
	 * @param response
	 * @param localCheck
	 * @return
	 */
	@RequestMapping(value = "/tempSaveLocalExamine", method = RequestMethod.POST)
	@CheckRepeatCommit
	@SysLogPoint(businessType = businessType.ADD_LOCAL_CHECK, dbOptType = dbType.ADD)
	@ResponseBody
	public ResponseJson tempSaveLocalExamine(Model model, HttpServletRequest request, HttpServletResponse response,
			LocalCheck localCheck, String chickItemList, String taskId, String checkStartDateTemp,
			String checkEndDateTemp) throws Exception {

		String administrativeNoticeNumber = request.getParameter("administrativeNoticeNumber");
		String administrativeNoticeAttachment = request.getParameter("administrativeNoticeAttachment");

		localCheck.setAdministrativeNoticeNumber(administrativeNoticeNumber);
		localCheck.setAdministrativeNoticeAttachment(administrativeNoticeAttachment);


		// logger.info("现场检查");
		if (!"".equals(checkEndDateTemp) && checkEndDateTemp != null) {
			// Date checkEndDate = DateUtil.getSimpleFormate(checkEndDateTemp);
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
			Date parse = format.parse(checkEndDateTemp);
			localCheck.setCheckEndDate(parse);

		}
		if (!"".equals(checkStartDateTemp) && checkStartDateTemp != null) {
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
			Date parse = format.parse(checkStartDateTemp);
			localCheck.setCheckStartDate(parse);
		}
		SysUsers sysUser = (SysUsers) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		ResponseJson json = null;
		try {
			json = LocalExamineService.tempSaveLocalExamine(localCheck, taskId, chickItemList, sysUser);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return json;
	}




	/**
	 * 上传附件
	 * @param request
	 * @param response
	 * @return
	 */

	@RequestMapping(value="/uploadFile", method=RequestMethod.POST)
	@ResponseBody
	public ResponseJson actionFileUpload(HttpServletRequest request, HttpServletResponse response) {
		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		List<SysFiles> list = null;
		try {
			list = LocalExamineService.uploadFiless(request, response, sysUsers);
			// 确保返回的 JSON 包含文件名和文件ID
			List<Map<String, String>> fileInfos = list.stream().map(file -> {
				Map<String, String> fileInfo = new HashMap<>();
				fileInfo.put("fileName", file.getFileName());
				fileInfo.put("fileId", file.getId().toString()); // 假设 fileId 是 Long 类型
				return fileInfo;
			}).collect(Collectors.toList());

			return new ResponseJson().success(HttpStatus.OK.toString(), "000", "上传成功", "文件上传成功", fileInfos);
		} catch (BusinessException e) {
			e.printStackTrace();
			return new ResponseJson().failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(), "000", "上传失败", "文件上传失败", null);
		}
	}

	/**
	 * 删除附件
	 *
	 * @param localCheakId 现场检查记录ID
	 * @param attachmentFileName 附件文件名
	 * @return 更新结果
	 */
	@RequestMapping(value = "/deleteFile", method = RequestMethod.POST)
	@ResponseBody
	public ResponseJson updateAttachmentFileName(
			@RequestParam("localCheakId") String localCheakId,
			@RequestParam("attachmentFileName") String attachmentFileName
	) {
		try {
			// 调用服务层方法进行更新
			LocalExamineService.updateAttachmentFileName(localCheakId, attachmentFileName);

			return new ResponseJson().success(
					HttpStatus.OK.toString(),
					SystemStatusCode.UPDATE_SUCCESS.toString(),
					"附件信息更新成功",
					null,
					null
			);
		} catch (Exception e) {
			e.printStackTrace();
			return new ResponseJson().failure(
					HttpStatus.INTERNAL_SERVER_ERROR.toString(),
					SystemStatusCode.UPDATE_FAILURE.toString(),
					"附件信息更新失败: " + e.getMessage(),
					null,
					null
			);
		}
	}




	// checkItemChoose
	/**
	 * 检查项的是否状态的修改
	 *
	 * @param request
	 * @param response
	 * @param localCheckitemId
	 * @param status
	 *            0为是，1为否
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/checkItemChoose", method = RequestMethod.POST)
	@ResponseBody
	public ResponseJson checkItemChoose(HttpServletRequest request, HttpServletResponse response,
			String localCheckitemId, String status) throws Exception {
		// LocalExamineService.taskManagerXczfXckfb(localCheck);

		ResponseJson json = LocalExamineService.checkItemChoose(localCheckitemId, status);

		// ModelAndView mav = new ModelAndView("taskManager/xczf-xckfb");
		return json;
	}

	// delCheckItem
	/**
	 * 删除单个的检查项内容 （根据主键id）
	 *
	 * @param request
	 * @param response
	 * @param status
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/delCheckItem", method = RequestMethod.POST)
	@ResponseBody
	public ResponseJson delCheckItem(HttpServletRequest request, HttpServletResponse response, String localCheckitemId)
			throws Exception {
		// LocalExamineService.taskManagerXczfXckfb(localCheck);
		ResponseJson json = LocalExamineService.delCheckItem(localCheckitemId);
		return json;
	}

	// editCheckItem
	/**
	 * 修改检查项备注信息
	 *
	 * @param request
	 * @param response
	 * @param localCheckitemId
	 * @param remarkCheckItemText
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/editRemarkCheckItem", method = RequestMethod.POST)
	@ResponseBody
	public ResponseJson editRemarkCheckItem(HttpServletRequest request, HttpServletResponse response,
			String localCheckitemId, String remarkCheckItemText) throws Exception {
		ResponseJson json = LocalExamineService.editRemarkCheckItem(localCheckitemId, remarkCheckItemText);
		return json;
	}


	// saveCheckItem
	/**
	 * 添加检查项信息
	 *
	 * @param request
	 * @param response
	 * @param localCheckitem
	 * @param taskId
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/saveCheckItem", method = RequestMethod.POST)
	@ResponseBody
	public ResponseJson saveCheckItem(HttpServletRequest request, HttpServletResponse response, String localCheckitem,
			String taskId, String localCheakId, String templateObjectType, String behId,String behFact) throws Exception {
		ResponseJson json = LocalExamineService.saveCheckItem(templateObjectType, localCheckitem, taskId, localCheakId,
				behId,behFact);
		return json;
	}

	/**
	 * 检查项输出
	 *
	 * @param request
	 * @param response
	 * @param localCheckitem
	 * @param taskId
	 * @param status
	 *            0系统模板，1为自定义和系统模板
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/checkItemList", method = RequestMethod.POST)
	@ResponseBody
	public LocalCheckItemBean checkItemList(HttpServletRequest request, HttpServletResponse response,
			LocalChickItemBean bean) throws Exception {
		try {
			SysUsers sysUser = (SysUsers) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			LocalCheckItemBean localCheckItemBean = LocalExamineService.checkItemList(bean, sysUser);
			return localCheckItemBean;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	// getDocUrlByLocalCheckId
	/**
	 * 根据主键的id查询pdf文件的url地址
	 *
	 * @param request
	 * @param response
	 * @param localCheckId
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/getDocUrlByLocalCheckId", method = RequestMethod.POST)
	@ResponseBody
	public ResponseJson getDocUrlByLocalCheckId(HttpServletRequest request, HttpServletResponse response,
			String localCheckId) throws Exception {
		ResponseJson json = LocalExamineService.getDocUrlByLocalCheckId(localCheckId);
		return json;
	}

	// checkItemRemark
	/**
	 * 根据检查项id查询备注信息
	 *
	 * @param request
	 * @param response
	 * @param localCheckitemId
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/checkItemRemark", method = RequestMethod.POST)
	@ResponseBody
	public ResponseJson checkItemRemark(HttpServletRequest request, HttpServletResponse response,
			String localCheckitemId) throws Exception {
		ResponseJson json = LocalExamineService.checkItemRemark(localCheckitemId);
		return json;
	}

	/**
	 * 文件的下载
	 *
	 * @param request
	 * @param response
	 */
	@SysLogPoint(businessType = businessType.DOWNLOAD_LOCAL_CHECK, dbOptType = dbType.DOWNLOAD)
	@RequestMapping(value = "/booksDown")
	public void booksDown(HttpServletRequest request, HttpServletResponse response,
			@RequestParam(value = "localCheckId", required = false) String localCheckId) {
		try {
			if (localCheckId != null && localCheckId != "") {
				LocalCheck localCheck = LocalExamineService.findDocUrlByLocalCheckId(localCheckId);
				if (localCheck != null) {
					String objectName = localCheck.getObjectName();
					String startDate = "";
					if(!ChangnengUtil.isNull(localCheck.getCheckStartDate())) {
						SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmm");
						Date checkStartDate = localCheck.getCheckStartDate();
						startDate = sdf.format(checkStartDate);
					}
					// FastDFSClient fastDFSClient = new
					// FastDFSClient("classpath:fdfs_client.conf");
					// FileUtil.setFileDownloadHeader(request,
					// response,objectName+".pdf");
					// output = new
					// BufferedOutputStream(response.getOutputStream());
					if (localCheck.getDocUrl() != null && localCheck.getDocUrl() != "") {
						FileUtil.downloadFDFSUtils(request, response, localCheck.getDocUrl(), objectName+"现场检查"+startDate);
						// fastDFSClient.download_file(localCheck.getDocUrl(),output);
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 现场检查表打印文书
	 *
	 * @param askId
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/print-xckfb-file")
	@SysLogPoint(businessType = businessType.PRINT_LOCAL_CHECK, dbOptType = dbType.EXPORT)
	public ModelAndView showFileModal(String localCheckId,String multiple, HttpServletRequest request, HttpServletResponse response) {
		ModelAndView view = new ModelAndView("taskManager/xcxf-xcbfb-print");
		try {
			LocalCheck record = LocalExamineService.getLocalCheckDocurl(localCheckId, 1);
			view.addObject("docUrl", record.getDocUrl());
			view.addObject("multiple", multiple);
		} catch (Exception e) {
			logger.info(e);
		}
		return view;
	}

	/**
	 *
	 * chickbooksDown检查文件的路径
	 */
	@RequestMapping(value = "/chickbooksDown")
	@ResponseBody
	public ResponseJson chickbooksDown(HttpServletRequest request,
			@RequestParam(value = "localCheckId", required = false) String localCheckId) {
		try {
			if (localCheckId != null && !"".equals(localCheckId)) {
				LocalCheck localCheck = LocalExamineService.findDocUrlByLocalCheckId(localCheckId);
				if (localCheck != null) {
					String docUrl = localCheck.getDocUrl();
					if (docUrl != null && !"".equals(docUrl)) {
						return new ResponseJson().success(HttpStatus.OK.toString(),
								SystemStatusCode.QUERY_SUCCESS.toString(), docUrl, null, localCheck.getDocUrl());
					} else {
						return new ResponseJson().success(HttpStatus.NOT_FOUND.toString(),
								SystemStatusCode.QUERY_SUCCESS.toString(), "路径为空！", null, localCheck.getDocUrl());
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 上传扫描件模态框
	 *
	 * @param taskId
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/scsmj")
	public ModelAndView picUploadPage(String taskId, HttpServletRequest request, HttpServletResponse response) {
		// 复用询问笔录的上传模态框，随后在页面中也是发送请求给询问笔录的controller中进行处理
		ModelAndView view = new ModelAndView("taskManager/xczf-xwbl-scsmjModel");
		return view;
	}

	/**
	 * 查看扫描件模态框
	 *
	 * @param taskId
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/cksmj")
	public ModelAndView showAppenPage(String lsFlag, String localCheckId, HttpServletRequest request,
			HttpServletResponse response) {
		List<ScanningAttachment> list = LocalExamineService.getAppendFile(localCheckId);
		// 跳转到询问笔录的查看扫描件模态框，因为都是用的ScanningAttachment，所以后续直接使用询问笔录的代码
//		ModelAndView view = new ModelAndView("taskManager/xczf-xwbl-cksmjModel");
//		view.addObject("fileList", list);
//		view.addObject("lsFlag", lsFlag);// 用于判断是否是从历史任务跳转，是：0；正常待办任务：1
		ModelAndView view = new ModelAndView("taskManager/filecase-onekeyAttachModal_new");
		view.addObject("tableName", "sequestration_info");
		view.addObject("generateId", "9C25D12EBE7B6967E0531D00A8C0A349");//一键生成的generateId
		view.addObject("itemIndex", "1");
		view.addObject("mType", "4");
		view.addObject("oId", "9C25D12EBE7B6967E0531D00A8C0A349");
		view.addObject("tdCode", "1");
		view.addObject("cId", "9C25D12EBE7B6967E0531D00A8C0A349");
		view.addObject("fileList", list);
		view.addObject("lsFlag", lsFlag);// 用于判断是否是从历史任务跳转，是：0；正常待办任务：1
		return view;
	}

	// saveTemplate
	/**
	 * 保存自定义模板
	 *
	 * @param request
	 * @param sceneCustomModel
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/saveTemplate")
	@CheckRepeatCommit
	@ResponseBody
	public ResponseJson saveTemplate(HttpServletRequest request, SceneCustomModel sceneCustomModel) throws Exception {
		ResponseJson json = null;
		try {
			SysUsers sysUser = (SysUsers) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			json = LocalExamineService.saveTemplate(sceneCustomModel, sysUser);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return json;
	}

	/**
	 * 现场检查自定义模版跳转处理
	 *
	 * @param lawObj
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/localExamine-custom-page")
	public ModelAndView askingCustomModePage(Model model, LawObjectTypeBean lawObj, HttpServletRequest request,
			HttpServletResponse response) {
		ModelAndView view = new ModelAndView("taskManager/customModel/xcjcCustomModeler");
		model.addAttribute("lawObj", lawObj);
		return view;
	}

	/**
	 * 现场检查自定义模版列表（根据用户，和执法对象类型区分）
	 *
	 * @param model
	 * @param lawObj
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/localExamine-custom-list", method = RequestMethod.POST)
	@ResponseBody
	public PageBean<AskingCustomModel> localExamineCustomModeList(Model model, AskingCustomBean customBean,
			HttpServletRequest request, HttpServletResponse response) {
		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		customBean.setUserId(sysUsers.getId());
		return LocalExamineService.localExamineCustomModeList(customBean);
	}

	/**
	 * 修改默认和常用项和删除
	 *
	 * @param customBean
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/usually-or-default-or-delete", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult setUsuallyOrDefaultOrDelete(AskingCustomBean customBean, HttpServletRequest request,
			HttpServletResponse response) throws Exception {
		JsonResult jsonResult = new JsonResult();
		try {
			jsonResult = LocalExamineService.setUsuallyOrDefaultOrDelete(customBean);
		} catch (Exception e) {
			jsonResult.setResult(Const.RESULT_ERROR);
			jsonResult.setMessage("设置失败");
			logger.info(e);
		}
		return jsonResult;
	}

	// chick-user-page
	/**
	 * 检查人模态框加载
	 *
	 * @param model
	 * @param lawObj
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/chick-user-page")
	public ModelAndView chickUserPage(Model model, HttpServletRequest request, HttpServletResponse response) {
		ModelAndView view = new ModelAndView("taskManager/chickUserModel");
		return view;
	}

	// localExamine-save-loction
	/**
	 * 检查项更换位置
	 *
	 * @param askingId
	 *            询问笔录id
	 * @param index
	 *            下标
	 * @param type
	 *            类型 0：上移 1：下移
	 * @param request
	 * @param response
	 * @return json
	 * @throws Exception
	 */
	@RequestMapping(value = "/localExamine-save-loction")
	@ResponseBody
	public JsonResult saveItemContentLoction(ModelerLocationBean locationBean, HttpServletRequest request,
			HttpServletResponse response) throws Exception {
		JsonResult json = new JsonResult();
		try {
			json = LocalExamineService.saveItemContentLoction(locationBean);
		} catch (Exception e) {
			json.setResult(Const.RESULT_ERROR);
			json.setMessage(e.getMessage());
			e.printStackTrace();
		}
		return json;
	}

	/**
	 * 执法对象模态框
	 * @param model
	 * @param request
	 * @param response
	 * @param lawObjectId
	 * @return
	 */
	@RequestMapping(value="/select-law-object-page")
	public ModelAndView selectLawObjectModelPage(Model model, HttpServletRequest request,HttpServletResponse response,
	String lawObjectId){
		ModelAndView mav = null;
		if(!ChangnengUtil.isNull(lawObjectId)){
			LawEnforceObjectWithBLOBs lawEnforceObject = zfdxManagerService.selectByPrimaryKey(lawObjectId);
			mav = new ModelAndView("model/lawObjectModel");
			mav.addObject("lawEnforceObject", lawEnforceObject);
		}
		return mav;
	}

	// relevance-behavior
	/**
	 * 现场检查自定义模版跳转处理
	 *
	 * @param lawObj
	 * @param request
	 * @param response
	 * @return
	 */

	@RequestMapping(value = "/relevance-behavior")
	public ModelAndView relevanceBehavior(Model model, HttpServletRequest request, HttpServletResponse response) {
		ModelAndView view = new ModelAndView("taskManager/xczf-xckfb-relbehmodel");
		return view;
	}

	@RequestMapping(value = "/behaviorguide")
	public ModelAndView rbehaviorguide(Model model, HttpServletRequest request, HttpServletResponse response,
			String behId) {
		ModelAndView view = new ModelAndView("taskManager/xczf-xckfb-behguidemodel");
		model.addAttribute("behId", behId);
		return view;
	}

	//--↓↓↓↓↓↓↓↓↓↓---
	/**
	 * 加载环境监管一件事历史数据
	 * @param request HTTP请求
	 * @param response HTTP响应
	 * @param taskId 任务ID
	 * @return 响应JSON
	 */
	@RequestMapping(value = "/loadEnvSupervisionData", method = RequestMethod.POST)
	@ResponseBody
	public ResponseJson loadEnvSupervisionData(HttpServletRequest request, HttpServletResponse response,
			String taskId) {
		ResponseJson json = new ResponseJson();

		try {
			if (taskId == null || taskId.trim().isEmpty()) {
				json.error("400", "PARAM_ERROR", "任务ID不能为空", null, null);
				return json;
			}

			// 根据任务ID查询现场检查记录
			LocalCheck localCheck = LocalExamineService.getLocalCheickItem(new LawObjectTypeBean() {{
				setTaskId(taskId);
			}});

			if (localCheck == null || localCheck.getId() == null) {
				json.success("200", "NO_DATA", "没有找到相关数据", "没有找到相关数据", new ArrayList<>());
				return json;
			}

			// 如果是环境监管一件事表单类型，加载对应的历史数据
			if (Integer.valueOf(1).equals(localCheck.getFormType())) {
				List<LocalCheckItem> envSupervisionItems = LocalExamineService.loadEnvSupervisionItems(localCheck.getId());
				json.success("200", "SUCCESS", "加载成功", "加载成功", envSupervisionItems);
			} else {
				json.success("200", "NO_DATA", "当前不是环境监管一件事表单", "当前不是环境监管一件事表单", new ArrayList<>());
			}

		} catch (Exception e) {
			logger.error("加载环境监管一件事历史数据失败，任务ID: {}", taskId, e);
			json.error("500", "SYSTEM_ERROR", "加载历史数据失败: " + e.getMessage(), null, null);
		}

		return json;
	}
	//----------↑↑↑↑↑↑-----

	public String selectUserParentTareaName(String code) {
		String name = "";
		if (!ChangnengUtil.isNull(code)) {
			// 区划的后4为若不是4个0则表示为3级（区、县）用户，则查出上级区划名称
			if (!code.substring(code.length() - 4, code.length()).equals("0000")) {
				name = taskMapper.getTareaNameByCode(code.substring(0, 4) + "0000");
			}
		}
		return name;
	}




	/**
	 * 展示文件的模态框
	 *
	 * @param fileId 文件ID
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/showFileModal")
	public ModelAndView showFileModal(String id, HttpServletRequest request, HttpServletResponse response) {
//		ModelAndView view = new ModelAndView("taskManager/fileShowModal");
		ModelAndView view = new ModelAndView("caseReadOnly/filecase-ylModal");
		System.err.println("fileId = " + id);
		try {
			SysFiles file = sysFilesMapper.selectByPrimaryKey(id);
			if (file != null) {
				view.addObject("fileInfo", JacksonUtils.toJsonString(file));
				view.addObject("fileInfoModel", file);
				view.addObject("ifGene", 1);
				view.addObject("fileName", file.getFileName());
			} else {
				view = new ModelAndView("error/404");
			}
		} catch (Exception e) {
			view = new ModelAndView("error/404");
			e.printStackTrace();
		}

		return view;
	}

	/**
	 * 获取附件信息
	 *
	 * @param localCheckId 现场检查记录ID
	 * @return 附件信息
	 */
	@RequestMapping(value = "/attSee", method = RequestMethod.POST)
	@ResponseBody
	public JsonResult getAttachments(String localCheckId) {
		JsonResult json = new JsonResult();

		try {
			// 根据 localCheckId 获取 administrativeNoticeAttachment 的值
			LocalCheck localCheck = LocalExamineService.getLocalCheckById(localCheckId);
			String administrativeNoticeAttachment = localCheck.getAdministrativeNoticeAttachment();
			System.err.println("administrativeNoticeAttachment = " + administrativeNoticeAttachment);

			if (!ChangnengUtil.isNull(administrativeNoticeAttachment)) {
				// 根据 administrativeNoticeAttachment 获取文件信息
				SysFiles attachment = sysFilesMapper.selectByPrimaryKey(administrativeNoticeAttachment);
				String fileUrl = attachment.getFileUrl();
				if (StringUtils.isNotEmpty(fileUrl)){
					//根据fileUrl截取最后一个点后面的后缀，获取文件后缀，如果后缀是图片，则fileType返回1，如果是pdf，则fileType返回2
					int dot = fileUrl.lastIndexOf('.');
					String fileType = fileUrl.substring(dot + 1).toLowerCase();
					System.out.println("附件fileType = " + fileType);
					if ("png".equals(fileType) || "jpg".equals(fileType) || "jpeg".equals(fileType) || "gif".equals(fileType)) {
						attachment.setFileType("1");
					} else if ("pdf".equals(fileType)) {
						attachment.setFileType("2");
					}
				}
				System.err.println("attachment = " + attachment);
				if (attachment != null) {
					json.setData(attachment);
					json.setCode("200");
					json.setMessage("获取附件信息成功");
				} else {
					json.setCode("500");
					json.setMessage("附件信息不存在");
				}
			} else {
				json.setCode("500");
				json.setMessage("administrativeNoticeAttachment 为空");
			}
		} catch (Exception e) {
			json.setCode("500");
			json.setMessage("获取附件信息失败: " + e.getMessage());
			e.printStackTrace();
		}

		return json;
	}
	/**
	 * 测试检查项配置树形结构接口
	 * 临时测试接口，用于验证 CheckItemConfigService#getTreeStructure 方法
	 */
	@RequestMapping(value = "/testTreeStructure", method = RequestMethod.GET)
	@ResponseBody
	public ResponseJson testTreeStructure(HttpServletRequest request, HttpServletResponse response) {
		try {
			// 调用服务方法获取树形结构
			List<CheckItemConfigTreeVO> treeStructure = checkItemConfigService.getTreeStructure();

			// 返回成功响应
			return new ResponseJson().success(
				HttpStatus.OK.toString(),
				"200",
				"获取树形结构成功",
				"检查项配置树形结构数据获取成功",
				treeStructure
			);
		} catch (Exception e) {
			// 记录错误日志
			logger.error("测试树形结构接口调用失败", e);

			// 返回错误响应
			return new ResponseJson().failure(
				HttpStatus.INTERNAL_SERVER_ERROR.toString(),
				"500",
				"获取树形结构失败",
				"系统异常：" + e.getMessage(),
				null
			);
		}
	}
}
