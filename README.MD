
20250729 新加需求

http://www.axshare.site/UER046
新增字段
```sql
ALTER TABLE LOCAL_CHECK ADD FORM_TYPE NUMBER(1) DEFAULT 0;
COMMENT ON COLUMN LOCAL_CHECK.FORM_TYPE IS '检查项类型：0=原有检查项，1=环境监管一件事';

ALTER TABLE LOCAL_CHECK_ITEM ADD FORM_TYPE NUMBER(1) DEFAULT 0;
ALTER TABLE LOCAL_CHECK_ITEM ADD CONFIG_ITEM_ID VARCHAR2(100);
ALTER TABLE LOCAL_CHECK_ITEM ADD PROBLEM_DESC VARCHAR2(2000);

COMMENT ON COLUMN LOCAL_CHECK_ITEM.FORM_TYPE IS '检查项类型：0=原有检查项，1=环境监管一件事';
COMMENT ON COLUMN LOCAL_CHECK_ITEM.CONFIG_ITEM_ID IS '关联CHECK_ITEM_CONFIG表ID';
COMMENT ON COLUMN LOCAL_CHECK_ITEM.PROBLEM_DESC IS '问题简述';
```


（已加）新增表：环境监管一件事-大项配置THINK_CHENK_ITEM
```sql
CREATE TABLE "CHENK_ITEM_CONFIG" 
   (	"ID" VARCHAR2(100), 
	"ITEM_NAME" VARCHAR2(2000),
        "PARENT_ID" VARCHAR2(100),
        "ITEM_SORT" NUMBER(38,0), 
	"CREATE_TIME" TIMESTAMP (6), 
	"REMARK" VARCHAR2(200)

   ) SEGMENT CREATION DEFERRED 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  TABLESPACE "USERS" ;

COMMENT ON TABLE ZXXD.CHENK_ITEM_CONFIG IS '环境监管一件事-检查项配置';
COMMENT ON COLUMN ZXXD.CHENK_ITEM_CONFIG.ITEM_NAME IS '检查项标题';
COMMENT ON COLUMN ZXXD.CHENK_ITEM_CONFIG.PARENT_ID IS '父级ID';
COMMENT ON COLUMN ZXXD.CHENK_ITEM_CONFIG.ITEM_SORT IS '排序';
COMMENT ON COLUMN ZXXD.CHENK_ITEM_CONFIG.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN ZXXD.CHENK_ITEM_CONFIG.REMARK IS '备注';

```
