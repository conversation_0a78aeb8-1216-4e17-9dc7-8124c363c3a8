package org.changneng.framework.frameworkbusiness.dao;


import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.changneng.framework.frameworkbusiness.entity.LocalCheck;
import org.changneng.framework.frameworkbusiness.entity.ScanningAttachment;

public interface LocalCheckMapper  {
    int deleteByPrimaryKey(String id);

    int insert(LocalCheck record);

    int insertSelective(LocalCheck record);

    LocalCheck selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(LocalCheck record);

    int updateByPrimaryKeyWithBLOBs(LocalCheck record);

    int updateByPrimaryKey(LocalCheck record);

	LocalCheck getLocalCheickItem(String taskId);

	void updateDocUrlByLocalCheck(@Param("docUrl")String docUrl,@Param("localChickId") String localChickId);
	
	LocalCheck getLocalCheickForColumn(String taskId);

	LocalCheck selectLocalItemByTaskId(@Param("taskId")String taskId);

	int updateObjNameByTaskId(@Param("objectName")String objectName,@Param("taskId")String taskId);

	//--↓↓↓↓↓↓↓↓↓↓---
	/**
	 * 根据任务ID和表单类型查询现场检查记录
	 * @param taskId 任务ID
	 * @param formType 表单类型：0=原有检查项，1=环境监管一件事
	 * @return 现场检查记录
	 */
	LocalCheck selectByTaskIdAndFormType(@Param("taskId") String taskId, @Param("formType") Integer formType);
	//----------↑↑↑↑↑↑-----
}
