package org.changneng.framework.frameworkbusiness.service;

import java.util.List;

import org.changneng.framework.frameworkbusiness.entity.*;
import org.changneng.framework.frameworkbusiness.entity.refinetemplate.SceneSysModelResult;
import org.changneng.framework.frameworkbusiness.entity.refinetemplate.SceneSysModelSeach;
import org.changneng.framework.frameworkcore.exception.BusinessException;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.changneng.framework.frameworkcore.utils.ResponseJson;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public interface LocalExamineService {
	
	/**
	 * 保存现场检查表信息
	 * @param localCheck
	 */
//	ResponseJson saveLocalExamine(LocalCheck localCheck,String taskId,String chickItemList ,SysUsers sysUser)throws Exception;
	ResponseJson saveLocalExamine(LocalCheck localCheck,String taskId,String chickItemList ,SysUsers sysUser, List<SysFiles> filesList)throws Exception;

	//--↓↓↓↓↓↓↓↓↓↓---
	/**
	 * 保存现场检查表信息（支持环境监管一件事）
	 * @param localCheck 现场检查对象
	 * @param taskId 任务ID
	 * @param chickItemList 原有检查项列表JSON
	 * @param sysUser 当前用户
	 * @param filesList 文件列表
	 * @param formType 表单类型：0=原有检查项，1=环境监管一件事
	 * @param envSupervisionData 环境监管一件事数据JSON
	 * @return 响应结果
	 * @throws Exception 异常
	 */
	ResponseJson saveLocalExamine(LocalCheck localCheck, String taskId, String chickItemList,
		SysUsers sysUser, List<SysFiles> filesList, Integer formType, String envSupervisionData) throws Exception;

	/**
	 * 保存环境监管一件事检查项
	 * @param localCheckId 本地检查ID
	 * @param envSupervisionData 环境监管一件事数据JSON
	 * @throws Exception 异常
	 */
	void saveEnvSupervisionItems(String localCheckId, String envSupervisionData) throws Exception;

	/**
	 * 加载环境监管一件事检查项
	 * @param localCheckId 本地检查ID
	 * @return 检查项列表
	 * @throws Exception 异常
	 */
	List<LocalCheckItem> loadEnvSupervisionItems(String localCheckId) throws Exception;
	//----------↑↑↑↑↑↑-----

	/**
	 * 保存现场检查表信息app
	 * @param localCheck
	 */
	ResponseJson appSaveLocalExamine(LocalCheck localCheck,String taskId,String chickItemList ,SysUsers sysUser)throws Exception;

	
	/**
	 * 初始化检查项，并判断检查项信息
	 * @param lawObj
	 * @param localChickId
	 * @return
	 */
	List<LocalCheckItem>  getChickItemItem(LawObjectTypeBean lawObj,String localChickId) throws Exception;
	
	/**
	 * 检查项 是否操作的json status 为1否 为0是
	 * @param localCheckitemId
	 * @param status
	 * @return
	 */
	ResponseJson checkItemChoose(String localCheckitemId, String status)  throws Exception;
	/**
	 * 根据主键id删除检查项的id
	 * @param localCheckitemId
	 * @return
	 * @throws Exception 
	 */
	ResponseJson delCheckItem(String localCheckitemId) throws Exception;
	/**
	 * 修改检查项备注的内容
	 * @param localCheckitemId
	 * @param remarkCheckItemText
	 * @return
	 */
	ResponseJson editRemarkCheckItem(String localCheckitemId,
			String remarkCheckItemText)throws Exception;
	/**
	 * 新增检查项
	 * @param localCheckitem
	 * @return
	 */
	ResponseJson saveCheckItem(String templateObjectType,String localCheckitem,String taskId,String localCheakId,String behId,String behFact)throws Exception;
	
	/**
	 * 根据任务的id查询现场检查表信息
	 * @param lawObj
	 * @return
	 */
	LocalCheck getLocalCheickItem(LawObjectTypeBean lawObj);
	/**
	 * 根据主键的id查询pdf文件的url地址
	 * @param localCheckId
	 * @return
	 */
	ResponseJson getDocUrlByLocalCheckId(String localCheckId);
	
	/**
	 * 根据主键的id查询pdf文件的url地址
	 * @param localCheckId
	 * @return
	 */
	LocalCheck findDocUrlByLocalCheckId(String localCheckId);
	
	/**
	 * 现场检查表文件打印
	 * @param localCheckId
	 * @param i
	 * @return
	 */
	LocalCheck getLocalCheckDocurl(String localCheckId, int i);
	/**
	 * 根据检查项id查询备注信息
	 * @param localCheckitemId
	 * @return
	 */
	ResponseJson checkItemRemark(String localCheckitemId);
	
	/**
	 * 根据现场检查ID查询ScanningAttachment
	 * @param localCheckId
	 * @return
	 */
	List<ScanningAttachment> getAppendFile(String localCheckId);
	/**
	 * 查询用户下的模板
	 * @param lawObjectType
	 * @return
	 */
	LocalCheckItemBean checkItemList(String lawObjectType,String localCheakId,
			String  status,String customModelerId,SysUsers sysUser);
	/**
	 * 查询用户下的模板
	 * @param lawObjectType
	 * @return
	 * @throws Exception 
	 */
	LocalCheckItemBean checkItemList(LocalChickItemBean bean,SysUsers sysUser) throws Exception;
		/**
	 *根据主键的id查询任务信息
	 * @param taskId
	 * @return
	 */
	Task selectByTaskId(String taskId);
	
	/**
	 * 保存自定义模板
	 * @param sceneCustomModel
	 * @return
	 */
	ResponseJson saveTemplate(SceneCustomModel sceneCustomModel,SysUsers sysUsers) throws Exception;
	
	/**
	 * 现场检查自定义模版列表（根据用户，和执法对象类型区分）
	 * @param customBean
	 * @param sysUsers 
	 * @return
	 */
	PageBean<AskingCustomModel> localExamineCustomModeList(
			AskingCustomBean customBean);
	
	/**
	 * 修改默认和常用项和删除模板
	 * @param customBean
	 * @return
	 */
	JsonResult setUsuallyOrDefaultOrDelete(AskingCustomBean customBean)throws Exception;
	/**
  	 * 检查项更换位置
  	 * @param askingId 询问笔录id
  	 * @param index 下标
  	 * @param type 类型 0：上移 1：下移
  	 * @param request
  	 * @param response
  	 * @return json
  	 * @throws Exception
  	 */
	JsonResult saveItemContentLoction(ModelerLocationBean locationBean)throws Exception;
	
	/**
	 * 暂存
	 * @param localCheck
	 * @param taskId
	 * @param chickItemList
	 * @param sysUser
	 * @return
	 * @throws Exception 
	 */
	ResponseJson tempSaveLocalExamine(LocalCheck localCheck, String taskId,
			String chickItemList, SysUsers sysUser) throws Exception;
	/**
	 * app获取现场表id
	 * @param taskId
	 * @return
	 */
	LocalCheck selectLocalItemByTaskId(String taskId);
	
	/**
	 * app 获取系统模板集合
	 * 20171204
	 * @return
	 */


	PageBean<SceneSysModelResult> sysTemplateList( SceneSysModelSeach sceneSysModelSeach);
	/**
	 * 根据系统模板的id查询检查项
	 * @param id
	 * @return
	 */
	List<SceneItemDatabase> sysTemplateList(String id);
	
	/**
	 * 根据自定义模板id查询检查项
	 * @param id
	 * @return
	 */
	List<SceneItemDatabase> customTemplateById(String id);

    List<SysFiles> uploadFiless(HttpServletRequest request, HttpServletResponse response, SysUsers sysUsers) throws BusinessException;


	void updateAttachmentFileName(String localCheakId, String attachmentFileName) throws BusinessException;

	List<SysFiles> getAttachmentsByLocalCheckId(String localCheckId);

	LocalCheck getLocalCheckById(String localCheckId);
}
