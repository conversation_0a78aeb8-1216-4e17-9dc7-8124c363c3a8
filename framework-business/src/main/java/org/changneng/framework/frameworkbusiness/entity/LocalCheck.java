package org.changneng.framework.frameworkbusiness.entity;

import java.util.Date;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;


public class LocalCheck {
    private String id;

    private String taskId;

    private String objectName;
    private String makeUnitName;

    private String levelCode;
    private String levelName;
	@NotEmpty(message="详细地址不能为空")
    private String address;

    @Length(min=0,max=100,message="法人代表应在{min}-{max}个字符")
    private String legalPerson;

    @Length(min=0,max=100,message="法人电话表应在{min}-{max}个字符")
    private String legalPhone;
    private String checkUserIds;

	 @NotEmpty(message="检查人不能为空")
	 @Length(min=0,max=2000,message="检查人应在{min}-{max}个字符")
    private String checkUserNames;
	 @NotEmpty(message="执法证号不能为空")
	 @Length(min=0,max=2000,message="执法证号应在{min}-{max}个字符")
    private String lawEnforcIds;
	 @Length(min=0,max=100,message="被监察单位现场负责人应在{min}-{max}个字符")
    private String localPerson;
	 @Length(min=0,max=100,message="现场负责人电话应在{min}-{max}个字符")
    private String localPersonPhone;
	 @Length(min=0,max=100,message="职务应在{min}-{max}个字符")
    private String localPersonJob;
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+08:00")
    private Date checkStartDate;
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+08:00")
    private Date checkEndDate;

	/*@NotEmpty(message="是否存在违法行为不能为空")*/
    private String isIllegalactCode;

    private String isIllegalactName;

    private String creatUserId;

    private String creatUserName;

    private Date lastUpdateDate;

    private String updateUserId;

    private String updateUserName;

    private String docUrl;
	/*@NotEmpty(message="监察小结不能为空")
    @Length(min=0,max=1300,message="监察小结应在{min}-{max}个字符")*/
    private String checkSummary;

    private String lawObjectId;

    private String  lawObjectType;

    private  String contributionName; //模板贡献者

    private String localCheckItemList;
    @NotEmpty(message="检查开始时间不能为空")
    private String checkStartDateTemp;


	@NotEmpty(message="检查结束时间不能为空")
    private String checkEndDateTemp;
	private String informDeptName;

    private String informLawIds;

    private String  updateObjectState; //同步信息1同步，0不同步 接收页面值
    private Integer synchronizationStatus;//同步信息1同步，0不同步 接收页面值
    @Length(min=0,max=1500,message="参与人员及其工作单位应在{min}-{max}个字符")
    private String  participant; //参与人
    private String saveStatus;//保存状态 0暂存 1保存

    private Integer isAppHandle;//是否为app登录，0是web，1是app，2web和app同是操作

    private String recordUserId;//记录人id
    @NotBlank(message="请选择记录人")
    private String recordUserName;//记录人名称


    private String administrativeNoticeNumber; // 行政检查通知书编号

    private String administrativeNoticeAttachment; // 行政检查通知书附件（关联文件id）

    private String attachmentFileName;// 行政检查通知书附件名称


	/*private String legalManIdCard;

	private String chargeManIdCard;*/

    private String multiple;
    /**
     * 检查项类型：0=原有检查项，1=环境监管一件事
     */
    private Integer formType;

    public Integer getFormType() {
        return formType;
    }

    public void setFormType(Integer formType) {
        this.formType = formType;
    }

    public String getMultiple() {
		return multiple;
	}

	public void setMultiple(String multiple) {
		this.multiple = multiple;
	}

	public String getRecordUserId() {
		return recordUserId;
	}

	public void setRecordUserId(String recordUserId) {
		this.recordUserId = recordUserId;
	}

	public String getRecordUserName() {
		return recordUserName;
	}

	public void setRecordUserName(String recordUserName) {
		this.recordUserName = recordUserName;
	}

	public Integer getSynchronizationStatus() {
		return synchronizationStatus;
	}

	public void setSynchronizationStatus(Integer synchronizationStatus) {
		this.synchronizationStatus = synchronizationStatus;
	}

	public Integer getIsAppHandle() {
		return isAppHandle;
	}

	public void setIsAppHandle(Integer isAppHandle) {
		this.isAppHandle = isAppHandle;
	}

	public String getUpdateObjectState() {
		return updateObjectState;
	}

	public void setUpdateObjectState(String updateObjectState) {
		this.updateObjectState = updateObjectState;
	}

	public String getParticipant() {
		return participant;
	}

	public void setParticipant(String participant) {
		this.participant = participant;
	}

	public String getSaveStatus() {
		return saveStatus;
	}

	public void setSaveStatus(String saveStatus) {
		this.saveStatus = saveStatus;
	}

	public String getInformDeptName() {
		return informDeptName;
	}

	public void setInformDeptName(String informDeptName) {
		this.informDeptName = informDeptName;
	}

	public String getInformLawIds() {
		return informLawIds;
	}

	public void setInformLawIds(String informLawIds) {
		this.informLawIds = informLawIds;
	}

	public String getCheckEndDateTemp() {
		return checkEndDateTemp;
	}

	public void setCheckEndDateTemp(String checkEndDateTemp) {
		this.checkEndDateTemp = checkEndDateTemp;
	}

	public String getCheckStartDateTemp() {
		return checkStartDateTemp;
	}

	public void setCheckStartDateTemp(String checkStartDateTemp) {
		this.checkStartDateTemp = checkStartDateTemp;
	}

	public String getLocalCheckItemList() {
		return localCheckItemList;
	}

	public void setLocalCheckItemList(String localCheckItemList) {
		this.localCheckItemList = localCheckItemList;
	}

	public String getContributionName() {
		return contributionName;
	}

	public void setContributionName(String contributionName) {
		this.contributionName = contributionName;
	}

	public String getLawObjectType() {
		return lawObjectType;
	}

	public void setLawObjectType(String lawObjectType) {
		this.lawObjectType = lawObjectType;
	}

	public String getMakeUnitName() {
		return makeUnitName;
	}

	public void setMakeUnitName(String makeUnitName) {
		this.makeUnitName = makeUnitName;
	}

	public String getLawObjectId() {
		return lawObjectId;
	}

	public void setLawObjectId(String lawObjectId) {
		this.lawObjectId = lawObjectId;
	}



	public String getId()  {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId == null ? null : taskId.trim();
    }

    public String getObjectName() {
        return objectName;
    }

    public void setObjectName(String objectName) {
        this.objectName = objectName == null ? null : objectName.trim();
    }

    public String getLevelCode() {
        return levelCode;
    }

    public void setLevelCode(String levelCode) {
        this.levelCode = levelCode == null ? null : levelCode.trim();
    }

    public String getLevelName() {
        return levelName;
    }

    public void setLevelName(String levelName) {
        this.levelName = levelName == null ? null : levelName.trim();
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    public String getLegalPerson() {
        return legalPerson;
    }

    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson == null ? null : legalPerson.trim();
    }

    public String getLegalPhone() {
        return legalPhone;
    }

    public void setLegalPhone(String legalPhone) {
        this.legalPhone = legalPhone == null ? null : legalPhone.trim();
    }

    public String getcheckUserIds() {
        return checkUserIds;
    }

    public void setcheckUserIds(String checkUserIds) {
        this.checkUserIds = checkUserIds == null ? null : checkUserIds.trim();
    }

    public String getcheckUserNames() {
        return checkUserNames;
    }

    public void setcheckUserNames(String checkUserNames) {
        this.checkUserNames = checkUserNames == null ? null : checkUserNames.trim();
    }

    public String getLawEnforcIds() {
        return lawEnforcIds;
    }

    public void setLawEnforcIds(String lawEnforcIds) {
        this.lawEnforcIds = lawEnforcIds == null ? null : lawEnforcIds.trim();
    }

    public String getLocalPerson() {
        return localPerson;
    }

    public void setLocalPerson(String localPerson) {
        this.localPerson = localPerson == null ? null : localPerson.trim();
    }

    public String getLocalPersonPhone() {
        return localPersonPhone;
    }

    public void setLocalPersonPhone(String localPersonPhone) {
        this.localPersonPhone = localPersonPhone == null ? null : localPersonPhone.trim();
    }

    public String getLocalPersonJob() {
        return localPersonJob;
    }

    public void setLocalPersonJob(String localPersonJob) {
        this.localPersonJob = localPersonJob == null ? null : localPersonJob.trim();
    }

    public Date getCheckStartDate() {
        return checkStartDate;
    }

    public void setCheckStartDate(Date checkStartDate) {
        this.checkStartDate = checkStartDate;
    }

    public Date getCheckEndDate() {
        return checkEndDate;
    }

    public void setCheckEndDate(Date checkEndDate) {
        this.checkEndDate = checkEndDate;
    }

    public String getIsIllegalactCode() {
        return isIllegalactCode;
    }

    public void setIsIllegalactCode(String isIllegalactCode) {
        this.isIllegalactCode = isIllegalactCode == null ? null : isIllegalactCode.trim();
    }

    public String getIsIllegalactName() {
        return isIllegalactName;
    }

    public void setIsIllegalactName(String isIllegalactName) {
        this.isIllegalactName = isIllegalactName == null ? null : isIllegalactName.trim();
    }

    public String getCreatUserId() {
        return creatUserId;
    }

    public void setCreatUserId(String creatUserId) {
        this.creatUserId = creatUserId == null ? null : creatUserId.trim();
    }

    public String getCreatUserName() {
        return creatUserName;
    }

    public void setCreatUserName(String creatUserName) {
        this.creatUserName = creatUserName == null ? null : creatUserName.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(String updateUserId) {
        this.updateUserId = updateUserId == null ? null : updateUserId.trim();
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName == null ? null : updateUserName.trim();
    }

    public String getDocUrl() {
        return docUrl;
    }

    public void setDocUrl(String docUrl) {
        this.docUrl = docUrl == null ? null : docUrl.trim();
    }


	public String getCheckSummary() {
		return checkSummary;
	}

	public void setCheckSummary(String checkSummary) {
		this.checkSummary = checkSummary;
	}



    public String getAdministrativeNoticeNumber() {
        return administrativeNoticeNumber;
    }

    public void setAdministrativeNoticeNumber(String administrativeNoticeNumber) {
        this.administrativeNoticeNumber = administrativeNoticeNumber;
    }

    public String getAdministrativeNoticeAttachment() {
        return administrativeNoticeAttachment;
    }

    public void setAdministrativeNoticeAttachment(String administrativeNoticeAttachment) {
        this.administrativeNoticeAttachment = administrativeNoticeAttachment;
    }

    public String getAttachmentFileName() {
        return attachmentFileName;
    }

    public void setAttachmentFileName(String attachmentFileName) {
        this.attachmentFileName = attachmentFileName;
    }

    /*public String getLegalManIdCard() {
		return legalManIdCard;
	}

	public void setLegalManIdCard(String legalManIdCard) {
		this.legalManIdCard = legalManIdCard;
	}

	public String getChargeManIdCard() {
		return chargeManIdCard;
	}

	public void setChargeManIdCard(String chargeManIdCard) {
		this.chargeManIdCard = chargeManIdCard;
	}

    */

    @Override
    public String toString() {
        return "LocalCheck{" +
                "id='" + id + '\'' +
                ", taskId='" + taskId + '\'' +
                ", objectName='" + objectName + '\'' +
                ", makeUnitName='" + makeUnitName + '\'' +
                ", levelCode='" + levelCode + '\'' +
                ", levelName='" + levelName + '\'' +
                ", address='" + address + '\'' +
                ", legalPerson='" + legalPerson + '\'' +
                ", legalPhone='" + legalPhone + '\'' +
                ", checkUserIds='" + checkUserIds + '\'' +
                ", checkUserNames='" + checkUserNames + '\'' +
                ", lawEnforcIds='" + lawEnforcIds + '\'' +
                ", localPerson='" + localPerson + '\'' +
                ", localPersonPhone='" + localPersonPhone + '\'' +
                ", localPersonJob='" + localPersonJob + '\'' +
                ", checkStartDate=" + checkStartDate +
                ", checkEndDate=" + checkEndDate +
                ", isIllegalactCode='" + isIllegalactCode + '\'' +
                ", isIllegalactName='" + isIllegalactName + '\'' +
                ", creatUserId='" + creatUserId + '\'' +
                ", creatUserName='" + creatUserName + '\'' +
                ", lastUpdateDate=" + lastUpdateDate +
                ", updateUserId='" + updateUserId + '\'' +
                ", updateUserName='" + updateUserName + '\'' +
                ", docUrl='" + docUrl + '\'' +
                ", checkSummary='" + checkSummary + '\'' +
                ", lawObjectId='" + lawObjectId + '\'' +
                ", lawObjectType='" + lawObjectType + '\'' +
                ", contributionName='" + contributionName + '\'' +
                ", localCheckItemList='" + localCheckItemList + '\'' +
                ", checkStartDateTemp='" + checkStartDateTemp + '\'' +
                ", checkEndDateTemp='" + checkEndDateTemp + '\'' +
                ", informDeptName='" + informDeptName + '\'' +
                ", informLawIds='" + informLawIds + '\'' +
                ", updateObjectState='" + updateObjectState + '\'' +
                ", synchronizationStatus=" + synchronizationStatus +
                ", participant='" + participant + '\'' +
                ", saveStatus='" + saveStatus + '\'' +
                ", isAppHandle=" + isAppHandle +
                ", recordUserId='" + recordUserId + '\'' +
                ", recordUserName='" + recordUserName + '\'' +
                ", multiple='" + multiple + '\'' +
                '}';
    }
}
